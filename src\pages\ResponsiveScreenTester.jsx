import React, { useState, useRef, useEffect } from 'react';
import SEO from '../components/SEO';

const ResponsiveScreenTester = () => {
  const [width, setWidth] = useState(375);
  const [height, setHeight] = useState(667);
  const [url, setUrl] = useState('https://example.com');
  const [isLoading, setIsLoading] = useState(false);
  const iframeRef = useRef(null);
  const [screenType, setScreenType] = useState('Mobile');
  
  // Common screen size presets
  const presets = [
    { name: 'Mobile S', width: 320, height: 568 },
    { name: 'Mobile M', width: 375, height: 667 },
    { name: 'Mobile L', width: 425, height: 812 },
    { name: 'Tablet', width: 768, height: 1024 },
    { name: 'Laptop', width: 1024, height: 768 },
    { name: 'Laptop L', width: 1440, height: 900 },
    { name: 'Desktop', width: 1920, height: 1080 }
  ];

  // Update screen type based on width
  useEffect(() => {
    if (width < 576) setScreenType('Mobile');
    else if (width < 992) setScreenType('Tablet');
    else if (width < 1440) setScreenType('Laptop');
    else setScreenType('Desktop');
  }, [width]);

  // Handle preset selection
  const handlePresetSelect = (preset) => {
    setWidth(preset.width);
    setHeight(preset.height);
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    loadPreview();
  };

  // Load the URL in the iframe
  const loadPreview = () => {
    setIsLoading(true);
    
    // Reset iframe
    if (iframeRef.current) {
      iframeRef.current.src = formatUrl(url);
    }
  };

  // Ensure URL has proper protocol
  const formatUrl = (inputUrl) => {
    if (!inputUrl.trim()) return 'about:blank';
    
    if (!inputUrl.startsWith('http://') && !inputUrl.startsWith('https://')) {
      return `https://${inputUrl}`;
    }
    return inputUrl;
  };

  // Handle iframe load event
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Responsive Screen Size Tester - Test Website Responsiveness"
        description="Test how your website looks on different screen sizes with our responsive design tester. Preview sites on mobile, tablet, laptop and desktop dimensions."
        keywords="responsive tester, screen size tester, website responsive test, mobile view tester, responsive design tool, device simulator"
      />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Responsive Screen Tester</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Preview how websites look across different devices and screen sizes
          </p>
        </div>
        
        <div className="grid grid-cols-1 gap-8">
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
              <h2 className="text-2xl font-bold text-white">Configure Test Settings</h2>
              <p className="text-blue-100 mt-1">Enter a URL and choose your display size</p>
            </div>
            
            <div className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
                    Website URL
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      id="url"
                      value={url}
                      onChange={(e) => setUrl(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://example.com"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="width" className="block text-sm font-medium text-gray-700 mb-1">
                      Width (px)
                    </label>
                    <input
                      type="number"
                      id="width"
                      value={width}
                      onChange={(e) => setWidth(Math.max(50, parseInt(e.target.value) || 320))}
                      min="50"
                      max="3000"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="height" className="block text-sm font-medium text-gray-700 mb-1">
                      Height (px)
                    </label>
                    <input
                      type="number"
                      id="height"
                      value={height}
                      onChange={(e) => setHeight(Math.max(50, parseInt(e.target.value) || 320))}
                      min="50"
                      max="3000"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Common Device Presets
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {presets.map(preset => (
                      <button
                        key={preset.name}
                        type="button"
                        onClick={() => handlePresetSelect(preset)}
                        className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                          width === preset.width && height === preset.height
                            ? 'bg-blue-100 text-blue-700 ring-2 ring-blue-500'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        {preset.name}
                      </button>
                    ))}
                  </div>
                </div>
                
                <button 
                  type="submit"
                  className="w-full md:w-auto px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                  Test Website
                </button>
              </form>
            </div>
          </div>
          
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6">
              <h2 className="text-2xl font-bold text-white">Preview</h2>
              <p className="text-purple-100 mt-1">
                Current Screen Type: <span className="font-medium">{screenType}</span> 
                ({width}px × {height}px)
              </p>
            </div>
            
            <div className="p-6">
              <div className="relative border border-gray-300 rounded-xl shadow-inner flex justify-center bg-gray-50 p-4">
                {/* Container that constrains iframe size */}
                <div
                  className="overflow-hidden bg-white shadow-md rounded-lg"
                  style={{ width: `${width}px`, height: `${height}px`, maxWidth: '100%' }}
                >
                  {isLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-80 z-10 rounded-lg">
                      <div className="flex flex-col items-center">
                        <svg className="animate-spin h-10 w-10 text-purple-600 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="text-sm text-gray-600">Loading preview...</span>
                      </div>
                    </div>
                  )}
                  <iframe
                    ref={iframeRef}
                    title="Website Preview"
                    src="about:blank"
                    className="w-full h-full"
                    style={{ resize: 'none', overflow: 'auto' }}
                    onLoad={handleIframeLoad}
                    sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
                    loading="lazy"
                  />
                </div>
              </div>
              
              <p className="text-sm text-gray-500 mt-4 text-center">
                Note: Some websites may block being displayed in iframes for security reasons.
              </p>
            </div>
          </div>
        </div>
        
        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Why Test Website Responsiveness?</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Responsive Design Importance</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Mobile traffic accounts for over 50% of global web traffic
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Google uses mobile-friendliness as a ranking factor
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-2">•</span>
                  Users are 5x more likely to leave a non-mobile-friendly site
                </li>
                <li className="flex items-center">
                  <span className="text-pink-500 mr-2">•</span>
                  Different devices have vastly different viewport dimensions
                </li>
              </ul>
              
              <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">Common Breakpoints</h3>
              <div className="space-y-2 text-gray-600">
                <div className="flex items-center">
                  <span className="w-24 text-blue-600 font-medium">Mobile:</span>
                  <span>320px - 480px</span>
                </div>
                <div className="flex items-center">
                  <span className="w-24 text-purple-600 font-medium">Tablet:</span>
                  <span>481px - 991px</span>
                </div>
                <div className="flex items-center">
                  <span className="w-24 text-indigo-600 font-medium">Laptop:</span>
                  <span>992px - 1439px</span>
                </div>
                <div className="flex items-center">
                  <span className="w-24 text-pink-600 font-medium">Desktop:</span>
                  <span>1440px and above</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Benefits for Developers</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <div>
                    <strong>Quick Testing:</strong> Test responsive designs without device switching
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">•</span>
                  <div>
                    <strong>Issue Detection:</strong> Identify layout problems across screen sizes
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <div>
                    <strong>Media Query Verification:</strong> Ensure CSS breakpoints work correctly
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-pink-500 mr-2">•</span>
                  <div>
                    <strong>UI Testing:</strong> Verify navigation and elements at various sizes
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  <div>
                    <strong>Collaboration:</strong> Share specific device views with team members
                  </div>
                </li>
              </ul>
              
              <div className="bg-blue-50 p-6 rounded-xl mt-8">
                <h3 className="text-lg font-semibold text-blue-700 mb-2">How To Use This Tool</h3>
                <ol className="space-y-2 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">1.</span>
                    <span>Enter the website URL you want to test</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">2.</span>
                    <span>Adjust the width and height manually or select a preset</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">3.</span>
                    <span>Click "Test Website" to load the preview</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">4.</span>
                    <span>Observe how the site renders at your chosen dimensions</span>
                  </li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveScreenTester;
