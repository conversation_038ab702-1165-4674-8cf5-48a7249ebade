import React from 'react';

const QRCustomization = ({ qrOptions, setQrOptions, qrData }) => {
  return (
    <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Customize QR Code</h3>
      <div className="space-y-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Size: {qrOptions.size}px
          </label>
          <input
            type="range"
            min="128"
            max="512"
            step="32"
            value={qrOptions.size}
            onChange={(e) => setQrOptions(prev => ({ ...prev, size: parseInt(e.target.value) }))}
            className="w-full"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Foreground</label>
            <input
              type="color"
              value={qrOptions.color.dark}
              onChange={(e) => setQrOptions(prev => ({
                ...prev,
                color: { ...prev.color, dark: e.target.value }
              }))}
              className="w-full h-10 rounded border border-gray-200"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Background</label>
            <input
              type="color"
              value={qrOptions.color.light}
              onChange={(e) => setQrOptions(prev => ({
                ...prev,
                color: { ...prev.color, light: e.target.value }
              }))}
              className="w-full h-10 rounded border border-gray-200"
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Error Correction</label>
          <select
            value={qrOptions.errorCorrectionLevel}
            onChange={(e) => setQrOptions(prev => ({ ...prev, errorCorrectionLevel: e.target.value }))}
            className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="L">Low (7%)</option>
            <option value="M">Medium (15%)</option>
            <option value="Q">Quartile (25%)</option>
            <option value="H">High (30%)</option>
          </select>
        </div>

        {qrData && (
          <div className="pt-2 border-t border-gray-200">
            <div className="text-xs text-gray-600">
              <strong>Data:</strong> {qrData?.length > 50 ? qrData.substring(0, 50) + '...' : qrData}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default QRCustomization;
