import React from 'react';
import { INPUT_STYLES, LABEL_STYLES } from '../utils/constants';

const VCardForm = ({ formData, updateFormData }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <label className={LABEL_STYLES}>First Name</label>
        <input
          type="text"
          value={formData.firstName}
          onChange={(e) => updateFormData('firstName', e.target.value)}
          className={INPUT_STYLES}
          placeholder="John"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Last Name</label>
        <input
          type="text"
          value={formData.lastName}
          onChange={(e) => updateFormData('lastName', e.target.value)}
          className={INPUT_STYLES}
          placeholder="Doe"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Organization</label>
        <input
          type="text"
          value={formData.organization}
          onChange={(e) => updateFormData('organization', e.target.value)}
          className={INPUT_STYLES}
          placeholder="Company Name"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Phone</label>
        <input
          type="tel"
          value={formData.phone}
          onChange={(e) => updateFormData('phone', e.target.value)}
          className={INPUT_STYLES}
          placeholder="+1234567890"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Email</label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => updateFormData('email', e.target.value)}
          className={INPUT_STYLES}
          placeholder="<EMAIL>"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Website</label>
        <input
          type="url"
          value={formData.website}
          onChange={(e) => updateFormData('website', e.target.value)}
          className={INPUT_STYLES}
          placeholder="https://example.com"
        />
      </div>
      <div className="space-y-2 sm:col-span-2">
        <label className={LABEL_STYLES}>Address</label>
        <input
          type="text"
          value={formData.address}
          onChange={(e) => updateFormData('address', e.target.value)}
          className={INPUT_STYLES}
          placeholder="123 Main St, City, State, ZIP"
        />
      </div>
    </div>
  );
};

export default VCardForm;
