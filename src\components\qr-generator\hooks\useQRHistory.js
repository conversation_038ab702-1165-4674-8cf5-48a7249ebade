import { useState, useEffect } from 'react';

/**
 * Custom hook for managing QR code history
 */
export const useQRHistory = () => {
  const [history, setHistory] = useState([]);

  // Load history from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('qrHistory');
    if (savedHistory) {
      try {
        setHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Failed to parse QR history:', error);
        setHistory([]);
      }
    }
  }, []);

  // Save history to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('qrHistory', JSON.stringify(history));
  }, [history]);

  /**
   * Add a new QR code to history
   */
  const addToHistory = (qrType, data, qrCode) => {
    const historyItem = {
      id: Date.now(),
      type: qrType,
      data: data,
      qrCode: qrCode,
      timestamp: new Date().toISOString()
    };
    
    // Keep only the last 10 items
    setHistory(prev => [historyItem, ...prev.slice(0, 9)]);
  };

  /**
   * Clear all history
   */
  const clearHistory = () => {
    setHistory([]);
  };

  /**
   * Remove a specific item from history
   */
  const removeFromHistory = (id) => {
    setHistory(prev => prev.filter(item => item.id !== id));
  };

  /**
   * Get recent history items (limited count)
   */
  const getRecentHistory = (count = 6) => {
    return history.slice(0, count);
  };

  return {
    history,
    addToHistory,
    clearHistory,
    removeFromHistory,
    getRecentHistory
  };
};
