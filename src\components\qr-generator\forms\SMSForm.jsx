import React from 'react';
import { INPUT_STYLES, LABEL_STYLES } from '../utils/constants';

const SMSForm = ({ formData, updateFormData }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Phone Number</label>
        <input
          type="tel"
          value={formData.number}
          onChange={(e) => updateFormData('number', e.target.value)}
          className={INPUT_STYLES}
          placeholder="+1234567890"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Message</label>
        <textarea
          value={formData.message}
          onChange={(e) => updateFormData('message', e.target.value)}
          className={INPUT_STYLES}
          placeholder="SMS message..."
          rows="3"
        />
      </div>
    </div>
  );
};

export default SMSForm;
