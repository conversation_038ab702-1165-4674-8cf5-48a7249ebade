import React from 'react';
import TextForm from './forms/TextForm';
import URLForm from './forms/URLForm';
import EmailForm from './forms/EmailForm';
import PhoneForm from './forms/PhoneForm';
import SMSForm from './forms/SMSForm';
import WiFiForm from './forms/WiFiForm';
import VCardForm from './forms/VCardForm';
import EventForm from './forms/EventForm';
import { BUTTON_STYLES } from './utils/constants';

const QRFormFields = ({ 
  qrType, 
  formData, 
  updateFormData, 
  generateQR, 
  generatedQR 
}) => {
  const currentFormData = formData[qrType] || {};

  const handleUpdateFormData = (field, value) => {
    updateFormData(qrType, field, value);
  };

  const renderForm = () => {
    const formProps = {
      formData: currentFormData,
      updateFormData: handleUpdateFormData
    };

    switch (qrType) {
      case 'text':
        return <TextForm {...formProps} />;
      case 'url':
        return <URLForm {...formProps} />;
      case 'email':
        return <EmailForm {...formProps} />;
      case 'phone':
        return <PhoneForm {...formProps} />;
      case 'sms':
        return <SMSForm {...formProps} />;
      case 'wifi':
        return <WiFiForm {...formProps} />;
      case 'vcard':
        return <VCardForm {...formProps} />;
      case 'event':
        return <EventForm {...formProps} />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Dynamic Form Fields */}
      <div className="space-y-4">
        {renderForm()}
      </div>

      {/* Save to History Button - Minimized */}
      {generatedQR && (
        <div className="flex justify-end">
          <button
            onClick={() => generateQR(true)}
            className="px-3 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white text-sm rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-sm"
          >
            💾 Save
          </button>
        </div>
      )}
    </div>
  );
};

export default QRFormFields;
