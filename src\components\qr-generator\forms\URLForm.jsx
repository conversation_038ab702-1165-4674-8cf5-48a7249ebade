import React from 'react';
import { INPUT_STYLES, LABEL_STYLES } from '../utils/constants';

const URLForm = ({ formData, updateFormData }) => {
  return (
    <div className="space-y-2">
      <label className={LABEL_STYLES}>Website URL</label>
      <input
        type="url"
        value={formData.url}
        onChange={(e) => updateFormData('url', e.target.value)}
        className={INPUT_STYLES}
        placeholder="https://example.com"
      />
    </div>
  );
};

export default URLForm;
