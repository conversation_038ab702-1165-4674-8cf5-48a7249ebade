import React, { useState } from 'react';
import { BUTTON_STYLES } from './utils/constants';
import { downloadQRCode, copyQRToClipboard, copyDataToClipboard } from './utils/qrDataGenerators';

const QRPreview = ({ 
  canvasRef, 
  generatedQR, 
  qrData, 
  qrType, 
  isGenerating 
}) => {
  const [showCopied, setShowCopied] = useState(false);

  const handleDownload = () => {
    downloadQRCode(generatedQR, qrType);
  };

  const handleCopyQR = () => {
    copyQRToClipboard(
      canvasRef.current,
      () => {
        setShowCopied(true);
        setTimeout(() => setShowCopied(false), 2000);
      },
      (error) => console.error('Failed to copy QR code:', error)
    );
  };

  const handleCopyData = () => {
    copyDataToClipboard(
      qrData,
      () => {
        setShowCopied(true);
        setTimeout(() => setShowCopied(false), 2000);
      },
      (error) => console.error('Failed to copy data:', error)
    );
  };

  return (
    <div className="bg-gray-50 p-6 rounded-lg border border-gray-100 text-center">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">QR Code Preview</h3>
      <div className="min-h-[280px] flex flex-col justify-center items-center">
        {isGenerating ? (
          <div className="text-gray-400">
            <div className="text-6xl mb-4">⏳</div>
            <p className="text-lg">Generating QR code...</p>
          </div>
        ) : (
          <>
            <canvas
              ref={canvasRef}
              className={`mb-4 ${generatedQR ? 'block' : 'hidden'}`}
              style={{
                maxWidth: '100%',
                height: 'auto',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                backgroundColor: 'white'
              }}
            />

            {generatedQR ? (
              <div className="flex justify-center">
                <button
                  onClick={handleDownload}
                  className={BUTTON_STYLES.download}
                >
                  📥 Download QR Code
                </button>
              </div>
            ) : (
              <div className="text-gray-400">
                <div className="text-6xl mb-4">📱</div>
                <p className="text-lg">QR code will appear here</p>
                <p className="text-sm mt-2">Start typing to generate automatically</p>
              </div>
            )}

            {showCopied && (
              <div className="mt-3 text-green-600 font-medium">
                ✅ Copied to clipboard!
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default QRPreview;
