import React, { useState } from 'react';
import SEO from '../components/SEO';

const SimpleCalculator = () => {
  const [display, setDisplay] = useState('0');
  const [equation, setEquation] = useState('');
  const [hasDecimal, setHasDecimal] = useState(false);
  const [lastWasOperator, setLastWasOperator] = useState(false);

  const handleNumber = (number) => {
    if (display === '0' || lastWasOperator) {
      setDisplay(number);
      setLastWasOperator(false);
    } else {
      setDisplay(display + number);
    }
  };

  const handleOperator = (operator) => {
    setEquation(display + ' ' + operator + ' ');
    setLastWasOperator(true);
    setHasDecimal(false);
  };

  const handleDecimal = () => {
    if (!hasDecimal) {
      setDisplay(display + '.');
      setHasDecimal(true);
    }
  };

  const handleEqual = () => {
    try {
      const result = eval(equation + display);
      setDisplay(String(result));
      setEquation('');
      setHasDecimal(String(result).includes('.'));
      setLastWasOperator(false);
    } catch (error) {
      setDisplay('Error');
      setEquation('');
    }
  };

  const handleClear = () => {
    setDisplay('0');
    setEquation('');
    setHasDecimal(false);
    setLastWasOperator(false);
  };

  const handleDelete = () => {
    if (display.length > 1) {
      const newDisplay = display.slice(0, -1);
      setDisplay(newDisplay);
      setHasDecimal(newDisplay.includes('.'));
    } else {
      setDisplay('0');
      setHasDecimal(false);
    }
  };

  return (
    <>
      <SEO 
        title="Simple Calculator - Basic Arithmetic Calculator | ToollyHub"
        description="Free online simple calculator for basic arithmetic operations. Add, subtract, multiply, and divide numbers with ease. Perfect for quick calculations and everyday math."
        keywords="simple calculator, basic calculator, arithmetic calculator, online calculator, math calculator, addition calculator, subtraction calculator, multiplication calculator, division calculator"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
            <h2 className="text-2xl font-bold text-white">Simple Calculator</h2>
            <p className="text-blue-100 mt-1">Basic arithmetic calculations</p>
          </div>
          
          <div className="p-6 space-y-6">
            <div className="bg-gray-50 p-6 rounded-xl shadow-inner border border-gray-200">
              <div className="text-right text-gray-600 text-sm h-6 mb-1 font-mono">{equation}</div>
              <div className="text-right text-3xl font-bold text-gray-800 break-all font-mono">{display}</div>
            </div>

            <div className="grid grid-cols-4 gap-3">
              <button onClick={handleClear} className="col-span-2 bg-red-500 text-white p-4 rounded-xl hover:bg-red-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                Clear
              </button>
              <button onClick={handleDelete} className="bg-gray-200 text-gray-700 p-4 rounded-xl hover:bg-gray-300 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md">
                ⌫
              </button>
              <button onClick={() => handleOperator('/')} className="bg-blue-500 text-white p-4 rounded-xl hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                ÷
              </button>

              {[7, 8, 9].map((num) => (
                <button
                  key={num}
                  onClick={() => handleNumber(num.toString())}
                  className="bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200"
                >
                  {num}
                </button>
              ))}
              <button onClick={() => handleOperator('*')} className="bg-blue-500 text-white p-4 rounded-xl hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                ×
              </button>

              {[4, 5, 6].map((num) => (
                <button
                  key={num}
                  onClick={() => handleNumber(num.toString())}
                  className="bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200"
                >
                  {num}
                </button>
              ))}
              <button onClick={() => handleOperator('-')} className="bg-blue-500 text-white p-4 rounded-xl hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                -
              </button>

              {[1, 2, 3].map((num) => (
                <button
                  key={num}
                  onClick={() => handleNumber(num.toString())}
                  className="bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200"
                >
                  {num}
                </button>
              ))}
              <button onClick={() => handleOperator('+')} className="bg-blue-500 text-white p-4 rounded-xl hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                +
              </button>

              <button onClick={() => handleNumber('0')} className="col-span-2 bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200">
                0
              </button>
              <button onClick={handleDecimal} className="bg-white text-gray-800 p-4 rounded-xl hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-md border border-gray-200">
                .
              </button>
              <button onClick={handleEqual} className="bg-green-500 text-white p-4 rounded-xl hover:bg-green-600 transition-all duration-200 transform hover:scale-105 font-semibold text-lg shadow-lg">
                =
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SimpleCalculator;