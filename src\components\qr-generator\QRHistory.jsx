import React, { useState } from 'react';
import { QR_TYPES } from './utils/constants';

const QRHistory = ({ history, clearHistory }) => {
  const [showCopied, setShowCopied] = useState(false);

  const handleDownload = (item) => {
    const link = document.createElement('a');
    link.download = `qr-${item.type}-${item.id}.png`;
    link.href = item.qrCode;
    link.click();
  };

  const handleCopy = async (item) => {
    try {
      const response = await fetch(item.qrCode);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      setShowCopied(true);
      setTimeout(() => setShowCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  if (history.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {history.slice(0, 6).map((item) => (
          <div key={item.id} className="bg-gray-50 p-4 rounded-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 capitalize">
                {QR_TYPES[item.type]?.icon} {QR_TYPES[item.type]?.label}
              </span>
              <span className="text-xs text-gray-500">
                {new Date(item.timestamp).toLocaleDateString()}
              </span>
            </div>
            <img
              src={item.qrCode}
              alt="QR Code"
              className="w-full h-24 object-contain bg-white rounded border mb-2"
            />
            <div className="text-xs text-gray-600 truncate mb-2" title={item.data}>
              {item.data}
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handleDownload(item)}
                className="flex-1 px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors"
              >
                📥
              </button>
              <button
                onClick={() => handleCopy(item)}
                className="flex-1 px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors"
              >
                📋
              </button>
            </div>
          </div>
        ))}
      </div>
      {history.length > 0 && (
        <div className="flex justify-center mt-6">
          <button
            onClick={clearHistory}
            className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium shadow-lg"
          >
            🗑️ Clear All History
          </button>
        </div>
      )}
      {showCopied && (
        <div className="text-center text-green-600 font-medium">
          ✅ Copied to clipboard!
        </div>
      )}
    </div>
  );
};

export default QRHistory;
