import React, { useState, useRef } from 'react';
import SEO from '../components/SEO';

const ImageWatermarking = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [watermarkType, setWatermarkType] = useState('text');
  const [watermarkText, setWatermarkText] = useState('© Your Name');
  const [watermarkLogo, setWatermarkLogo] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  const [position, setPosition] = useState('bottom-right');
  const [opacity, setOpacity] = useState(50);
  const [fontSize, setFontSize] = useState(24);
  const [fontColor, setFontColor] = useState('#ffffff');
  const [fontFamily, setFontFamily] = useState('Arial');
  const [logoSize, setLogoSize] = useState(20);
  const [watermarkedImage, setWatermarkedImage] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const fileInputRef = useRef(null);
  const logoInputRef = useRef(null);
  const canvasRef = useRef(null);

  // Position options
  const positions = [
    { value: 'top-left', label: 'Top Left' },
    { value: 'top-center', label: 'Top Center' },
    { value: 'top-right', label: 'Top Right' },
    { value: 'center-left', label: 'Center Left' },
    { value: 'center', label: 'Center' },
    { value: 'center-right', label: 'Center Right' },
    { value: 'bottom-left', label: 'Bottom Left' },
    { value: 'bottom-center', label: 'Bottom Center' },
    { value: 'bottom-right', label: 'Bottom Right' },
  ];

  // Font families
  const fontFamilies = [
    'Arial', 'Helvetica', 'Times New Roman', 'Georgia', 'Verdana', 
    'Courier New', 'Impact', 'Comic Sans MS', 'Trebuchet MS', 'Palatino'
  ];

  // Handle main image selection
  const handleImageSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setImageDimensions({ width: img.width, height: img.height });
        };
        img.src = e.target.result;
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
      setWatermarkedImage(null);
    } else {
      alert('Please select a valid image file.');
    }
  };

  // Handle logo selection
  const handleLogoSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setWatermarkLogo(file);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    } else {
      alert('Please select a valid logo image file.');
    }
  };

  // Calculate position coordinates
  const getPositionCoordinates = (canvasWidth, canvasHeight, watermarkWidth, watermarkHeight) => {
    const padding = 20;
    let x, y;

    switch (position) {
      case 'top-left':
        x = padding;
        y = padding;
        break;
      case 'top-center':
        x = (canvasWidth - watermarkWidth) / 2;
        y = padding;
        break;
      case 'top-right':
        x = canvasWidth - watermarkWidth - padding;
        y = padding;
        break;
      case 'center-left':
        x = padding;
        y = (canvasHeight - watermarkHeight) / 2;
        break;
      case 'center':
        x = (canvasWidth - watermarkWidth) / 2;
        y = (canvasHeight - watermarkHeight) / 2;
        break;
      case 'center-right':
        x = canvasWidth - watermarkWidth - padding;
        y = (canvasHeight - watermarkHeight) / 2;
        break;
      case 'bottom-left':
        x = padding;
        y = canvasHeight - watermarkHeight - padding;
        break;
      case 'bottom-center':
        x = (canvasWidth - watermarkWidth) / 2;
        y = canvasHeight - watermarkHeight - padding;
        break;
      case 'bottom-right':
      default:
        x = canvasWidth - watermarkWidth - padding;
        y = canvasHeight - watermarkHeight - padding;
        break;
    }

    return { x, y };
  };

  // Apply watermark
  const applyWatermark = () => {
    if (!selectedImage || !imagePreview) return;
    if (watermarkType === 'text' && !watermarkText.trim()) {
      alert('Please enter watermark text.');
      return;
    }
    if (watermarkType === 'logo' && !watermarkLogo) {
      alert('Please select a logo image.');
      return;
    }

    setIsProcessing(true);

    const img = new Image();
    img.onload = () => {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Draw main image
      ctx.drawImage(img, 0, 0);
      
      // Set opacity
      ctx.globalAlpha = opacity / 100;
      
      if (watermarkType === 'text') {
        // Apply text watermark
        ctx.font = `${fontSize}px ${fontFamily}`;
        ctx.fillStyle = fontColor;
        ctx.textBaseline = 'top';
        
        // Measure text
        const textMetrics = ctx.measureText(watermarkText);
        const textWidth = textMetrics.width;
        const textHeight = fontSize;
        
        // Get position
        const { x, y } = getPositionCoordinates(canvas.width, canvas.height, textWidth, textHeight);
        
        // Add text shadow for better visibility
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
        
        ctx.fillText(watermarkText, x, y);
        
        // Reset shadow
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        
        // Convert to blob and create download URL
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            setWatermarkedImage(url);
          } else {
            alert('Failed to apply watermark. Please try again.');
          }
          setIsProcessing(false);
        }, selectedImage.type, 0.9);
        
      } else {
        // Apply logo watermark
        const logoImg = new Image();
        logoImg.onload = () => {
          // Calculate logo dimensions
          const maxLogoSize = Math.min(canvas.width, canvas.height) * (logoSize / 100);
          const logoAspectRatio = logoImg.width / logoImg.height;
          
          let logoWidth, logoHeight;
          if (logoAspectRatio > 1) {
            logoWidth = maxLogoSize;
            logoHeight = maxLogoSize / logoAspectRatio;
          } else {
            logoHeight = maxLogoSize;
            logoWidth = maxLogoSize * logoAspectRatio;
          }
          
          // Get position
          const { x, y } = getPositionCoordinates(canvas.width, canvas.height, logoWidth, logoHeight);
          
          // Draw logo
          ctx.drawImage(logoImg, x, y, logoWidth, logoHeight);
          
          // Convert to blob and create download URL
          canvas.toBlob((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              setWatermarkedImage(url);
            } else {
              alert('Failed to apply watermark. Please try again.');
            }
            setIsProcessing(false);
          }, selectedImage.type, 0.9);
        };
        
        logoImg.src = logoPreview;
      }
    };
    
    img.src = imagePreview;
  };

  // Download watermarked image
  const downloadImage = () => {
    if (!watermarkedImage) return;
    
    const link = document.createElement('a');
    link.href = watermarkedImage;
    link.download = `watermarked_${selectedImage.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Reset all states
  const resetTool = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setWatermarkedImage(null);
    setWatermarkLogo(null);
    setLogoPreview(null);
    setWatermarkText('© Your Name');
    setPosition('bottom-right');
    setOpacity(50);
    setFontSize(24);
    setFontColor('#ffffff');
    setFontFamily('Arial');
    setLogoSize(20);
    setImageDimensions({ width: 0, height: 0 });
    if (fileInputRef.current) fileInputRef.current.value = '';
    if (logoInputRef.current) logoInputRef.current.value = '';
  };

  return (
    <>
      <SEO
        title="Image Watermarking Tool - Add Text/Logo Watermarks | ToollyHub"
        description="Add custom text or logo watermarks to your images. Control position, opacity, size, and styling for perfect watermark placement."
        keywords="image watermark, add watermark, text watermark, logo watermark, image protection, copyright watermark, watermark tool"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-6xl mx-auto">
          {/* Modern Header */}
          <div className="bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-2xl shadow-xl p-8 mb-8">
            <h1 className="text-4xl font-bold text-white text-center mb-2">Image Watermarking Tool</h1>
            <p className="text-purple-100 text-center text-lg">Add text or logo watermarks to protect your images</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Controls Section */}
            <div className="lg:col-span-1 bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6">
                <h2 className="text-xl font-bold text-white">Watermark Settings</h2>
                <p className="text-indigo-100 mt-1">Customize your watermark</p>
              </div>
              <div className="p-6 space-y-6">
                
                {/* Main Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Select Image</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageSelect}
                      className="hidden"
                      id="imageInput"
                    />
                    <label htmlFor="imageInput" className="cursor-pointer">
                      <div className="space-y-2">
                        <div className="text-4xl">📁</div>
                        <p className="text-gray-600">Click to select image</p>
                        <p className="text-sm text-gray-400">JPG, PNG, GIF, WebP</p>
                      </div>
                    </label>
                  </div>
                </div>

                {selectedImage && (
                  <>
                    {/* Image Info */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-medium text-gray-800 mb-2">Image Info</h3>
                      <p className="text-sm text-gray-600">
                        <strong>File:</strong> {selectedImage.name}
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Size:</strong> {imageDimensions.width} × {imageDimensions.height} px
                      </p>
                    </div>

                    {/* Watermark Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Watermark Type</label>
                      <div className="flex space-x-3">
                        <button
                          className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                            watermarkType === 'text' 
                              ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-md transform scale-105' 
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                          onClick={() => setWatermarkType('text')}
                        >
                          Text
                        </button>
                        <button
                          className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                            watermarkType === 'logo' 
                              ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-md transform scale-105' 
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                          onClick={() => setWatermarkType('logo')}
                        >
                          Logo
                        </button>
                      </div>
                    </div>

                    {/* Text Watermark Settings */}
                    {watermarkType === 'text' && (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Watermark Text</label>
                          <input
                            type="text"
                            value={watermarkText}
                            onChange={(e) => setWatermarkText(e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                            placeholder="Enter watermark text"
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                            <input
                              type="number"
                              value={fontSize}
                              onChange={(e) => setFontSize(parseInt(e.target.value) || 24)}
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                              min="8"
                              max="200"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Font Color</label>
                            <input
                              type="color"
                              value={fontColor}
                              onChange={(e) => setFontColor(e.target.value)}
                              className="w-full h-10 border border-gray-300 rounded-lg cursor-pointer"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Font Family</label>
                          <select
                            value={fontFamily}
                            onChange={(e) => setFontFamily(e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          >
                            {fontFamilies.map((font) => (
                              <option key={font} value={font}>{font}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    )}

                    {/* Logo Watermark Settings */}
                    {watermarkType === 'logo' && (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3">Select Logo</label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-indigo-400 transition-colors">
                            <input
                              ref={logoInputRef}
                              type="file"
                              accept="image/*"
                              onChange={handleLogoSelect}
                              className="hidden"
                              id="logoInput"
                            />
                            <label htmlFor="logoInput" className="cursor-pointer">
                              {logoPreview ? (
                                <img src={logoPreview} alt="Logo preview" className="max-w-full h-16 mx-auto rounded" />
                              ) : (
                                <div className="space-y-2">
                                  <div className="text-2xl">🖼️</div>
                                  <p className="text-gray-600 text-sm">Click to select logo</p>
                                </div>
                              )}
                            </label>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Logo Size: {logoSize}%
                          </label>
                          <input
                            type="range"
                            min="5"
                            max="50"
                            value={logoSize}
                            onChange={(e) => setLogoSize(parseInt(e.target.value))}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                          />
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Small</span>
                            <span>Large</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Position Settings */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Position</label>
                      <div className="grid grid-cols-3 gap-2">
                        {positions.map((pos) => (
                          <button
                            key={pos.value}
                            className={`p-2 text-xs rounded-lg border transition-all duration-200 ${
                              position === pos.value
                                ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                                : 'border-gray-200 hover:border-indigo-300 text-gray-700'
                            }`}
                            onClick={() => setPosition(pos.value)}
                          >
                            {pos.label}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Opacity Settings */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Opacity: {opacity}%
                      </label>
                      <input
                        type="range"
                        min="10"
                        max="100"
                        value={opacity}
                        onChange={(e) => setOpacity(parseInt(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>Transparent</span>
                        <span>Opaque</span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={applyWatermark}
                        disabled={isProcessing}
                        className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isProcessing ? 'Processing...' : 'Apply Watermark'}
                      </button>
                      <button
                        onClick={resetTool}
                        className="px-6 py-3 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md"
                      >
                        Reset
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Preview Section */}
            <div className="lg:col-span-2 bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 p-6">
                <h2 className="text-xl font-bold text-white">Preview & Download</h2>
                <p className="text-orange-100 mt-1">Original and watermarked image preview</p>
              </div>
              <div className="p-6">
                {imagePreview ? (
                  <div className="space-y-6">
                    {/* Original Image */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-3">Original Image</h3>
                      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <img
                          src={imagePreview}
                          alt="Original"
                          className="max-w-full h-auto max-h-64 mx-auto rounded-lg shadow-sm"
                        />
                      </div>
                    </div>

                    {/* Watermarked Image */}
                    {watermarkedImage && (
                      <div>
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="text-lg font-semibold text-gray-800">Watermarked Image</h3>
                          <button
                            onClick={downloadImage}
                            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-md text-sm"
                          >
                            📥 Download
                          </button>
                        </div>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <img
                            src={watermarkedImage}
                            alt="Watermarked"
                            className="max-w-full h-auto max-h-64 mx-auto rounded-lg shadow-sm"
                          />
                        </div>

                        {/* Watermark Info */}
                        <div className="bg-blue-50 p-4 rounded-lg mt-4">
                          <h4 className="font-medium text-gray-800 mb-2">Watermark Applied</h4>
                          <div className="space-y-1 text-sm text-gray-600">
                            <p><strong>Type:</strong> {watermarkType === 'text' ? 'Text' : 'Logo'}</p>
                            {watermarkType === 'text' && (
                              <>
                                <p><strong>Text:</strong> "{watermarkText}"</p>
                                <p><strong>Font:</strong> {fontFamily}, {fontSize}px</p>
                              </>
                            )}
                            <p><strong>Position:</strong> {positions.find(p => p.value === position)?.label}</p>
                            <p><strong>Opacity:</strong> {opacity}%</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🏷️</div>
                    <p className="text-gray-500 text-lg">Upload an image to add watermark</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Hidden canvas for image processing */}
          <canvas ref={canvasRef} style={{ display: 'none' }} />

          {/* SEO Content */}
          <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">About Image Watermarking</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Protect Your Images</h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  Watermarking is essential for protecting your digital images from unauthorized use.
                  Add text or logo watermarks to establish ownership and maintain brand recognition.
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    <strong>Text Watermarks:</strong> Add copyright notices or signatures
                  </li>
                  <li className="flex items-center">
                    <span className="text-purple-500 mr-2">•</span>
                    <strong>Logo Watermarks:</strong> Brand your images with company logos
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">•</span>
                    <strong>Flexible Positioning:</strong> 9 different placement options
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Customization Options</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-orange-500 mr-2">•</span>
                    Adjustable opacity for subtle or prominent watermarks
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-500 mr-2">•</span>
                    Multiple font families and sizes for text
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-2">•</span>
                    Custom colors and logo sizing options
                  </li>
                  <li className="flex items-center">
                    <span className="text-teal-500 mr-2">•</span>
                    High-quality output with original image preservation
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageWatermarking;
