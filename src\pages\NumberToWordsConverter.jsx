import React, { useState } from 'react';
import SEO from '../components/SEO';

const NumberToWordsConverter = () => {
  const [number, setNumber] = useState('');
  const [words, setWords] = useState('');
  const [copied, setCopied] = useState(false);

  // Function to convert numbers to words
  const convertNumberToWords = (num) => {
    const ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine',
      'ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
    const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
    const scales = ['', 'thousand', 'million', 'billion', 'trillion', 'quadrillion', 'quintillion'];

    // Handle zero separately
    if (num === 0) return 'zero';

    // Handle negative numbers
    const isNegative = num < 0;
    num = Math.abs(num);

    // Convert a 3-digit group into words
    const convertGroup = (n) => {
      let result = '';
      
      // Handle hundreds
      if (n >= 100) {
        result += ones[Math.floor(n / 100)] + ' hundred';
        n %= 100;
        if (n !== 0) result += ' and ';
      }
      
      // Handle tens and ones
      if (n > 0) {
        if (n < 20) {
          result += ones[n];
        } else {
          result += tens[Math.floor(n / 10)];
          if (n % 10 > 0) {
            result += '-' + ones[n % 10];
          }
        }
      }
      
      return result;
    };

    // Main function to process the number
    const processToWords = (n) => {
      if (n === 0) return '';
      
      // Split into groups of 3 digits
      const groups = [];
      while (n > 0) {
        groups.push(n % 1000);
        n = Math.floor(n / 1000);
      }
      
      // Process each group
      let result = '';
      for (let i = 0; i < groups.length; i++) {
        const groupWords = convertGroup(groups[i]);
        if (groupWords !== '') {
          if (i > 0) {
            result = groupWords + ' ' + scales[i] + (result ? ' ' + result : '');
          } else {
            result = groupWords;
          }
        }
      }
      
      return result;
    };

    const result = processToWords(num);
    return isNegative ? 'negative ' + result : result;
  };

  const handleConvert = (e) => {
    e.preventDefault();
    if (!number.trim()) return;

    const num = parseFloat(number);
    if (isNaN(num)) {
      setWords('Please enter a valid number');
      return;
    }

    // Check if it's a very large number
    if (Math.abs(num) > 999999999999999) {
      setWords('Number is too large to convert');
      return;
    }

    // Handle decimals
    if (Number.isInteger(num)) {
      setWords(convertNumberToWords(num));
    } else {
      const parts = number.split('.');
      const wholePart = parseInt(parts[0]);
      const decimalPart = parts[1];
      
      let result = convertNumberToWords(wholePart);
      
      if (decimalPart) {
        result += ' point';
        for (let i = 0; i < decimalPart.length; i++) {
          result += ' ' + convertNumberToWords(parseInt(decimalPart[i]));
        }
      }
      
      setWords(result);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(words);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Number to Words Converter - Convert Numbers to Text"
        description="Convert numbers to words easily. Perfect for writing checks, formal documents, or educational purposes. Supports integers and decimals."
        keywords="number to words, number in words, number to text converter, spell numbers, number spelling, convert numbers to text"
      />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Number to Words Converter</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Convert any number into written words. Perfect for checks, legal documents, and educational purposes.
          </p>
        </div>
        
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl mb-8">
          <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6">
            <h2 className="text-2xl font-bold text-white">Convert Numbers to Words</h2>
            <p className="text-indigo-100 mt-1">Enter any number to see it written out in words</p>
          </div>
          
          <div className="p-6">
            <form onSubmit={handleConvert} className="space-y-6">
              <div>
                <label htmlFor="number-input" className="block text-sm font-medium text-gray-700 mb-2">
                  Enter Number
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    id="number-input"
                    value={number}
                    onChange={(e) => setNumber(e.target.value)}
                    placeholder="e.g., 42, 1234.56, -789"
                    className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    required
                  />
                </div>
              </div>
              
              <button
                type="submit"
                className="w-full px-6 py-3 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white font-semibold rounded-lg hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
              >
                Convert to Words
              </button>
            </form>
            
            {words && (
              <div className="mt-6 space-y-4">
                <div className="flex justify-between">
                  <h3 className="text-lg font-semibold text-gray-800">Result</h3>
                  <button
                    onClick={copyToClipboard}
                    className="flex items-center text-sm text-indigo-600 hover:text-indigo-800 focus:outline-none"
                  >
                    {copied ? (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        Copied!
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                          <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                        </svg>
                        Copy
                      </>
                    )}
                  </button>
                </div>
                <div className="p-4 bg-gray-50 rounded-xl border border-gray-100">
                  <p className="text-lg text-gray-700 capitalize">
                    {words}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Why Convert Numbers to Words?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Common Applications</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-2">•</span>
                  Writing checks and financial documents
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Legal documents and contracts
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Educational teaching materials
                </li>
                <li className="flex items-center">
                  <span className="text-orange-500 mr-2">•</span>
                  Formal reports and presentations
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">•</span>
                  Accessibility for screen readers
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Features of Our Converter</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <span>Supports positive and negative numbers</span>
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <span>Handles decimal numbers properly</span>
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <span>Works with large numbers (up to 999 trillion)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <span>Grammatically correct spellings</span>
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <span>One-click copy functionality</span>
                </li>
              </ul>
            </div>
          </div>
          
          <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">Common Examples</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse">
              <thead>
                <tr>
                  <th className="px-4 py-2 bg-gray-50 border text-left">Number</th>
                  <th className="px-4 py-2 bg-gray-50 border text-left">Words</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="px-4 py-2 border">7</td>
                  <td className="px-4 py-2 border">Seven</td>
                </tr>
                <tr>
                  <td className="px-4 py-2 border bg-gray-50">42</td>
                  <td className="px-4 py-2 border bg-gray-50">Forty-two</td>
                </tr>
                <tr>
                  <td className="px-4 py-2 border">123</td>
                  <td className="px-4 py-2 border">One hundred and twenty-three</td>
                </tr>
                <tr>
                  <td className="px-4 py-2 border bg-gray-50">1,000</td>
                  <td className="px-4 py-2 border bg-gray-50">One thousand</td>
                </tr>
                <tr>
                  <td className="px-4 py-2 border">1,234.56</td>
                  <td className="px-4 py-2 border">One thousand two hundred and thirty-four point five six</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NumberToWordsConverter;
