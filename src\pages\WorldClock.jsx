import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const WorldClock = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);
  
  // List of cities with their timezone offsets
  const cities = [
    { name: "New York", timezone: "America/New_York", offset: -4, country: "USA", emoji: "🇺🇸" },
    { name: "Los Angeles", timezone: "America/Los_Angeles", offset: -7, country: "USA", emoji: "🇺🇸" },
    { name: "London", timezone: "Europe/London", offset: 1, country: "UK", emoji: "🇬🇧" },
    { name: "Paris", timezone: "Europe/Paris", offset: 2, country: "France", emoji: "🇫🇷" },
    { name: "Berlin", timezone: "Europe/Berlin", offset: 2, country: "Germany", emoji: "🇩🇪" },
    { name: "Moscow", timezone: "Europe/Moscow", offset: 3, country: "Russia", emoji: "🇷🇺" },
    { name: "Dubai", timezone: "Asia/Dubai", offset: 4, country: "UAE", emoji: "🇦🇪" },
    { name: "Mumbai", timezone: "Asia/Kolkata", offset: 5.5, country: "India", emoji: "🇮🇳" },
    { name: "Singapore", timezone: "Asia/Singapore", offset: 8, country: "Singapore", emoji: "🇸🇬" },
    { name: "Tokyo", timezone: "Asia/Tokyo", offset: 9, country: "Japan", emoji: "🇯🇵" },
    { name: "Sydney", timezone: "Australia/Sydney", offset: 10, country: "Australia", emoji: "🇦🇺" },
    { name: "Auckland", timezone: "Pacific/Auckland", offset: 12, country: "New Zealand", emoji: "🇳🇿" },
  ];

  // Function to format time with leading zeros
  const formatTime = (time) => {
    return time < 10 ? `0${time}` : time;
  };

  // Function to get time for a specific city based on its UTC offset
  const getCityTime = (offset) => {
    const localTime = currentTime;
    
    // Get UTC time in milliseconds
    const utc = localTime.getTime() + (localTime.getTimezoneOffset() * 60000);
    
    // Create new Date object for the city using supplied offset
    const cityTime = new Date(utc + (3600000 * offset));
    
    const hours = formatTime(cityTime.getHours());
    const minutes = formatTime(cityTime.getMinutes());
    const seconds = formatTime(cityTime.getSeconds());
    
    return `${hours}:${minutes}:${seconds}`;
  };

  // Get current date in string format
  const getCurrentDate = () => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return currentTime.toLocaleDateString(undefined, options);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="World Clock - Current Time in Major Cities"
        description="Check the current time in major cities around the world including New York, London, Tokyo, Sydney, and more with our free world clock tool."
        keywords="world clock, international time, global time zones, current time worldwide, time zone converter, world time zones"
      />

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">World Clock</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Check the current time in major cities around the world. Stay connected across time zones.
          </p>
        </div>

        {/* Current local time display */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl mb-8">
          <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6">
            <h2 className="text-2xl font-bold text-white">Your Local Time</h2>
            <p className="text-indigo-100 mt-1">Current time in your timezone</p>
          </div>

          <div className="p-8 text-center">
            <div className="text-6xl font-bold my-6 text-indigo-600 font-mono">
              {formatTime(currentTime.getHours())}:
              {formatTime(currentTime.getMinutes())}:
              {formatTime(currentTime.getSeconds())}
            </div>
            <p className="text-lg text-gray-600">{getCurrentDate()}</p>
          </div>
        </div>

        {/* World cities grid */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
            <h2 className="text-2xl font-bold text-white">World Time Zones</h2>
            <p className="text-blue-100 mt-1">Current time in major cities worldwide</p>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cities.map((city) => (
                <div key={city.name} className="bg-gray-50 p-6 rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 hover:bg-gray-100">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="text-lg font-semibold text-gray-800">{city.name}</h3>
                    <span className="text-2xl" role="img" aria-label={city.country}>{city.emoji}</span>
                  </div>
                  <p className="text-sm text-gray-500 mb-3">{city.country}</p>
                  <div className="text-3xl font-bold text-blue-600 font-mono">
                    {getCityTime(city.offset)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">About World Time Zones</h2>
          <p className="text-gray-600 mb-6">
            Time zones are regions on Earth that observe a uniform standard time for legal, commercial, and social purposes.
            Time zones tend to follow the boundaries of countries and their subdivisions because it is convenient for areas in close commercial
            or other communication to keep the same time.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Why Use a World Clock?</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-2">•</span>
                  Planning international calls and video conferences
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Coordinating with colleagues across time zones
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Scheduling travel and understanding jet lag
                </li>
                <li className="flex items-center">
                  <span className="text-orange-500 mr-2">•</span>
                  Following global events and financial markets
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Time Zone Information</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">UTC and GMT</h4>
                  <p className="text-gray-600 text-sm">
                    Coordinated Universal Time (UTC) is the primary time standard by which the world regulates clocks and time.
                    It is similar to Greenwich Mean Time (GMT), although there are slight technical differences.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Daylight Saving Time</h4>
                  <p className="text-gray-600 text-sm">
                    Many regions observe daylight saving time (DST), which typically involves advancing clocks by one hour during summer months.
                    This tool shows standard time offsets and may not reflect current DST adjustments.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">How This World Clock Works</h3>
          <p className="text-gray-600 mb-4">
            Our World Clock tool uses JavaScript to calculate the current time in various cities around the world based on your device's clock and known UTC offsets.
            No server communication is needed, so times are always up-to-date and accurate.
          </p>

          <p className="text-gray-600">
            Time zones are calculated as offsets from Coordinated Universal Time (UTC). For example, New York is typically UTC-4 or UTC-5 depending on
            daylight saving time, while Tokyo is UTC+9. These offsets allow us to know the exact time anywhere in the world at any given moment.
          </p>

          <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
            <p className="text-blue-800 text-sm">
              <strong>Note:</strong> This tool provides approximate times and may not account for recent changes in time zone policies or daylight saving time transitions.
              For absolute precision in official contexts, please consult official time sources for each location.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorldClock;
