import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const GradientGenerator = () => {
  const [colors, setColors] = useState(['#4158D0', '#C850C0']);
  const [gradientType, setGradientType] = useState('linear');
  const [direction, setDirection] = useState('to right');
  const [cssCode, setCssCode] = useState('');
  const [copied, setCopied] = useState(false);
  const [presets, setPresets] = useState([
    { name: 'Sunset', colors: ['#FF512F', '#F09819'], type: 'linear', direction: 'to right' },
    { name: 'Ocean', colors: ['#2193b0', '#6dd5ed'], type: 'linear', direction: 'to bottom' },
    { name: 'Purple Love', colors: ['#cc2b5e', '#753a88'], type: 'linear', direction: 'to right' },
    { name: 'Summer', colors: ['#22c1c3', '#fdbb2d'], type: 'linear', direction: '135deg' },
    { name: 'Sky', colors: ['#1488CC', '#2B32B2'], type: 'linear', direction: 'to top' },
    { name: 'Rainbow', colors: ['#00F5A0', '#00D9F5', '#9E00FF'], type: 'linear', direction: 'to right' },
    { name: 'Radial Sunset', colors: ['#EB3349', '#F45C43'], type: 'radial', direction: 'circle' },
    { name: 'Cosmic Fusion', colors: ['#ff00cc', '#333399'], type: 'linear', direction: '45deg' },
  ]);
  
  // Directions for linear gradients
  const linearDirections = [
    'to right',
    'to left',
    'to bottom',
    'to top',
    'to bottom right',
    'to bottom left',
    'to top right',
    'to top left',
    '45deg',
    '135deg',
    '225deg',
    '315deg'
  ];
  
  // Shapes for radial gradients
  const radialShapes = [
    'circle',
    'ellipse'
  ];
  
  // Update CSS code when any of the gradient properties change
  useEffect(() => {
    generateCssCode();
  }, [colors, gradientType, direction]);
  
  // Generate CSS code based on current settings
  const generateCssCode = () => {
    let colorStops = '';
    
    colors.forEach((color, index) => {
      colorStops += color;
      if (index < colors.length - 1) {
        colorStops += ', ';
      }
    });
    
    let cssValue = '';
    if (gradientType === 'linear') {
      cssValue = `linear-gradient(${direction}, ${colorStops})`;
    } else if (gradientType === 'radial') {
      cssValue = `radial-gradient(${direction}, ${colorStops})`;
    }
    
    setCssCode(`background-image: ${cssValue};`);
  };
  
  // Handle color change for a specific index
  const handleColorChange = (index, value) => {
    const newColors = [...colors];
    newColors[index] = value;
    setColors(newColors);
  };
  
  // Add a new color
  const addColor = () => {
    if (colors.length < 5) { // Limit to 5 colors for simplicity
      // Generate a random color
      const randomColor = '#' + Math.floor(Math.random()*16777215).toString(16);
      setColors([...colors, randomColor]);
    }
  };
  
  // Remove a color at specific index
  const removeColor = (index) => {
    if (colors.length > 2) { // Minimum 2 colors required for gradient
      const newColors = [...colors];
      newColors.splice(index, 1);
      setColors(newColors);
    }
  };
  
  // Copy CSS code to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(cssCode).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };
  
  // Apply a preset
  const applyPreset = (preset) => {
    setColors(preset.colors);
    setGradientType(preset.type);
    setDirection(preset.direction);
  };
  
  // Save current gradient as a preset
  const saveAsPreset = () => {
    const presetName = prompt('Enter a name for this preset:');
    if (presetName) {
      const newPreset = {
        name: presetName,
        colors: [...colors],
        type: gradientType,
        direction: direction
      };
      setPresets([...presets, newPreset]);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="CSS Gradient Generator - Create Beautiful Gradients"
        description="Create stunning CSS gradients for your website with our easy-to-use gradient generator. Choose colors, direction, and type to generate ready-to-use CSS code."
        keywords="gradient generator, CSS gradient, background gradient, web design tool, color gradient, linear gradient, radial gradient, gradient CSS code"
      />

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">CSS Gradient Generator</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Create stunning CSS gradients for your website with our easy-to-use gradient generator. Choose colors, direction, and type to generate ready-to-use CSS code.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Controls Section */}
          <div className="lg:col-span-1 bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6">
              <h2 className="text-2xl font-bold text-white">Gradient Settings</h2>
              <p className="text-purple-100 mt-1">Customize your gradient properties</p>
            </div>

            <div className="p-6 space-y-6">
              {/* Gradient Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Gradient Type</label>
                <div className="flex space-x-3">
                  <button
                    className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                      gradientType === 'linear'
                        ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setGradientType('linear')}
                  >
                    Linear
                  </button>
                  <button
                    className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                      gradientType === 'radial'
                        ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setGradientType('radial')}
                  >
                    Radial
                  </button>
                </div>
              </div>

              {/* Direction/Shape */}
              <div>
                <label htmlFor="direction" className="block text-sm font-medium text-gray-700 mb-2">
                  {gradientType === 'linear' ? 'Direction' : 'Shape'}
                </label>
                <select
                  id="direction"
                  value={direction}
                  onChange={(e) => setDirection(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                >
                  {gradientType === 'linear' ? (
                    linearDirections.map((dir) => (
                      <option key={dir} value={dir}>
                        {dir}
                      </option>
                    ))
                  ) : (
                    radialShapes.map((shape) => (
                      <option key={shape} value={shape}>
                        {shape}
                      </option>
                    ))
                  )}
                </select>
              </div>

              {/* Color Stops */}
              <div>
                <div className="flex justify-between items-center mb-3">
                  <label className="block text-sm font-medium text-gray-700">Colors</label>
                  {colors.length < 5 && (
                    <button
                      onClick={addColor}
                      className="text-sm text-purple-600 hover:text-purple-800 font-medium"
                    >
                      + Add Color
                    </button>
                  )}
                </div>

                {colors.map((color, index) => (
                  <div key={index} className="flex items-center mb-4 p-3 bg-gray-50 rounded-lg">
                    <input
                      type="color"
                      value={color}
                      onChange={(e) => handleColorChange(index, e.target.value)}
                      className="w-12 h-12 rounded-lg border-2 border-gray-300 mr-3 cursor-pointer"
                    />
                    <input
                      type="text"
                      value={color}
                      onChange={(e) => handleColorChange(index, e.target.value)}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                    {colors.length > 2 && (
                      <button
                        onClick={() => removeColor(index)}
                        className="ml-3 p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    )}
                  </div>
                ))}
              </div>

              {/* Save as Preset */}
              <button
                onClick={saveAsPreset}
                className="w-full px-6 py-3 border-2 border-purple-500 text-purple-600 rounded-lg hover:bg-purple-50 transition-all duration-200 font-medium hover:scale-[1.02]"
              >
                Save as Preset
              </button>
            </div>
          </div>

          {/* Preview and Code Section */}
          <div className="lg:col-span-2 space-y-8">
            {/* Gradient Preview */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6">
                <h2 className="text-2xl font-bold text-white">Live Preview</h2>
                <p className="text-indigo-100 mt-1">See your gradient in real-time</p>
              </div>

              <div className="p-6">
                <div
                  className="h-64 rounded-xl shadow-inner flex items-center justify-center border border-gray-200"
                  style={{ backgroundImage: gradientType === 'linear'
                    ? `linear-gradient(${direction}, ${colors.join(', ')})`
                    : `radial-gradient(${direction}, ${colors.join(', ')})`
                  }}
                >
                  <span className="px-6 py-3 bg-white/90 backdrop-blur-sm rounded-xl font-semibold text-gray-800 shadow-lg">
                    Preview
                  </span>
                </div>
              </div>
            </div>

            {/* CSS Code */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-green-500 to-green-600 p-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-2xl font-bold text-white">CSS Code</h2>
                    <p className="text-green-100 mt-1">Ready-to-use CSS for your project</p>
                  </div>
                  <button
                    onClick={copyToClipboard}
                    className="flex items-center px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors font-medium"
                  >
                    {copied ? (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        Copied!
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                          <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                        </svg>
                        Copy
                      </>
                    )}
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="bg-gray-900 p-4 rounded-xl font-mono text-sm overflow-x-auto text-green-400">
                  {cssCode}
                </div>
              </div>
            </div>

            {/* Presets */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6">
                <h2 className="text-2xl font-bold text-white">Gradient Presets</h2>
                <p className="text-orange-100 mt-1">Click any preset to apply it instantly</p>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {presets.map((preset, index) => (
                    <div
                      key={index}
                      onClick={() => applyPreset(preset)}
                      className="h-20 rounded-xl shadow-md cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 border border-gray-200"
                      style={{
                        backgroundImage: preset.type === 'linear'
                          ? `linear-gradient(${preset.direction}, ${preset.colors.join(', ')})`
                          : `radial-gradient(${preset.direction}, ${preset.colors.join(', ')})`
                      }}
                      title={preset.name}
                    >
                      <div className="h-full w-full flex items-end justify-center p-2">
                        <span className="text-xs bg-white/90 backdrop-blur-sm px-3 py-1 rounded-lg text-gray-800 truncate max-w-full font-medium shadow-sm">
                          {preset.name}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Using CSS Gradients in Web Design</h2>
          <p className="text-gray-600 mb-6">
            CSS gradients allow you to display smooth transitions between two or more specified colors. They are a popular way to add depth,
            visual interest, and modern aesthetics to websites without using image files.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Types of CSS Gradients</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-700 mb-2">Linear Gradients</h4>
                  <p className="text-gray-600 text-sm">
                    Linear gradients transition colors along a straight line. You can control the direction of this line using keywords
                    (like "to right" or "to bottom") or specific angles (like "45deg" or "90deg").
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-700 mb-2">Radial Gradients</h4>
                  <p className="text-gray-600 text-sm">
                    Radial gradients transition colors outward from a central point in a circular or elliptical pattern.
                    You can specify whether you want a circle or ellipse and control its position.
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Benefits of CSS Gradients</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">•</span>
                  <span><strong>Performance:</strong> Rendered by browser, faster than images</span>
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <span><strong>Scalability:</strong> Perfect scaling without quality loss</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  <span><strong>Customization:</strong> Easy to modify and adjust</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  <span><strong>Reduced Requests:</strong> No external image files needed</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span><strong>Accessibility:</strong> Maintains text readability</span>
                </li>
              </ul>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">Popular Uses for Gradients</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <span className="text-purple-500 mr-2">•</span>
                Background elements for headers and hero sections
              </li>
              <li className="flex items-center">
                <span className="text-indigo-500 mr-2">•</span>
                Button styling for depth and visual interest
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">•</span>
                Call-to-action elements that draw attention
              </li>
            </ul>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <span className="text-orange-500 mr-2">•</span>
                Separators between content sections
              </li>
              <li className="flex items-center">
                <span className="text-blue-500 mr-2">•</span>
                Card backgrounds for modern designs
              </li>
              <li className="flex items-center">
                <span className="text-red-500 mr-2">•</span>
                Text effects with careful accessibility implementation
              </li>
            </ul>
          </div>

          <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">Tips for Effective Gradients</h3>
          <ol className="space-y-2 text-gray-600">
            <li className="flex items-start">
              <span className="text-purple-500 mr-2 font-semibold">1.</span>
              <span>Choose colors that complement your brand identity and design palette</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-500 mr-2 font-semibold">2.</span>
              <span>Consider contrast for text readability if placing content over gradients</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-500 mr-2 font-semibold">3.</span>
              <span>Use subtle gradients for sophisticated designs, bold ones for dramatic impact</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-500 mr-2 font-semibold">4.</span>
              <span>Experiment with different directions and angles for unique effects</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-500 mr-2 font-semibold">5.</span>
              <span>Consider using gradients with transparency to overlay on images</span>
            </li>
          </ol>

          <div className="mt-8 p-6 bg-purple-50 rounded-xl border border-purple-200">
            <p className="text-purple-800">
              <strong>Pro Tip:</strong> With our Gradient Generator tool, you can easily create, preview, and get the exact CSS code for any gradient design you envision.
              Simply select your colors, choose a gradient type and direction, then copy the generated code directly into your CSS.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GradientGenerator;
