import React from 'react';
import { INPUT_STYLES, LABEL_STYLES } from '../utils/constants';

const EventForm = ({ formData, updateFormData }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2 sm:col-span-2">
        <label className={LABEL_STYLES}>Event Title</label>
        <input
          type="text"
          value={formData.title}
          onChange={(e) => updateFormData('title', e.target.value)}
          className={INPUT_STYLES}
          placeholder="Event Title"
        />
      </div>
      <div className="space-y-2 sm:col-span-2">
        <label className={LABEL_STYLES}>Location</label>
        <input
          type="text"
          value={formData.location}
          onChange={(e) => updateFormData('location', e.target.value)}
          className={INPUT_STYLES}
          placeholder="Event Location"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Start Date</label>
        <input
          type="date"
          value={formData.startDate}
          onChange={(e) => updateFormData('startDate', e.target.value)}
          className={INPUT_STYLES}
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Start Time</label>
        <input
          type="time"
          value={formData.startTime}
          onChange={(e) => updateFormData('startTime', e.target.value)}
          className={INPUT_STYLES}
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>End Date</label>
        <input
          type="date"
          value={formData.endDate}
          onChange={(e) => updateFormData('endDate', e.target.value)}
          className={INPUT_STYLES}
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>End Time</label>
        <input
          type="time"
          value={formData.endTime}
          onChange={(e) => updateFormData('endTime', e.target.value)}
          className={INPUT_STYLES}
        />
      </div>
      <div className="space-y-2 sm:col-span-2">
        <label className={LABEL_STYLES}>Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => updateFormData('description', e.target.value)}
          className={INPUT_STYLES}
          placeholder="Event description..."
          rows="3"
        />
      </div>
    </div>
  );
};

export default EventForm;
