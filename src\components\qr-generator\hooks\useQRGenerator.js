import { useState, useEffect, useRef } from 'react';
import QRCode from 'qrcode';
import { DEFAULT_QR_OPTIONS } from '../utils/constants';
import { generateQRData } from '../utils/qrDataGenerators';

/**
 * Custom hook for QR code generation logic
 */
export const useQRGenerator = (qrType, formData, onQRGenerated) => {
  const [qrOptions, setQrOptions] = useState(DEFAULT_QR_OPTIONS);
  const [generatedQR, setGeneratedQR] = useState('');
  const [qrData, setQrData] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const canvasRef = useRef(null);

  /**
   * Generate QR Code
   */
  const generateQR = async (saveToHistory = false) => {
    const data = generateQRData(qrType, formData);

    if (!data.trim()) {
      setGeneratedQR('');
      setQrData('');
      return;
    }

    setIsGenerating(true);

    try {
      const canvas = canvasRef.current;
      if (!canvas) {
        setIsGenerating(false);
        return;
      }

      // Set canvas size to match QR options
      canvas.width = qrOptions.size;
      canvas.height = qrOptions.size;

      await new Promise((resolve, reject) => {
        QRCode.toCanvas(canvas, data, {
          ...qrOptions,
          width: qrOptions.size,
          height: qrOptions.size
        }, (error) => {
          if (error) {
            console.error('QR generation error:', error);
            reject(error);
          } else {
            const dataURL = canvas.toDataURL();
            setGeneratedQR(dataURL);
            setQrData(data);

            // Notify parent component
            onQRGenerated?.(dataURL, data, saveToHistory);
            resolve();
          }
        });
      });
    } catch (error) {
      console.error('Error generating QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Auto-generate QR code when form data changes (with debounce)
  useEffect(() => {
    if (!canvasRef.current) return;

    const timeoutId = setTimeout(() => {
      generateQR(false); // Don't save to history on auto-generation
    }, 300); // Debounce for 300ms

    return () => clearTimeout(timeoutId);
  }, [formData, qrType]);

  // Regenerate QR code when options change
  useEffect(() => {
    if (generatedQR && canvasRef.current) {
      generateQR(false);
    }
  }, [qrOptions]);

  // Generate initial QR code after component mounts
  useEffect(() => {
    const initializeQR = () => {
      if (canvasRef.current && formData[qrType]) {
        generateQR(false);
      } else {
        // Retry after a short delay if canvas isn't ready
        setTimeout(initializeQR, 100);
      }
    };

    const timer = setTimeout(initializeQR, 100);
    return () => clearTimeout(timer);
  }, []); // Run only once on mount

  return {
    canvasRef,
    qrOptions,
    setQrOptions,
    generatedQR,
    qrData,
    isGenerating,
    generateQR
  };
};
