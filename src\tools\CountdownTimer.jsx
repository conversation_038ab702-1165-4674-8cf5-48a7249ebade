import React, { useState, useEffect, useRef } from 'react';
import SEO from '../components/SEO';

const CountdownTimer = () => {
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0); // in seconds
  const [error, setError] = useState('');
  const [isPaused, setIsPaused] = useState(false);
  const [initialTime, setInitialTime] = useState(0);
  const timerRef = useRef(null);
  const [showCompletionMessage, setShowCompletionMessage] = useState(false);

  // Sound effects for timer completion
  const audioRef = useRef(null);

  useEffect(() => {
    if (isRunning && !isPaused && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime <= 1) {
            clearInterval(timerRef.current);
            setIsRunning(false);
            setShowCompletionMessage(true);
            // Play sound when timer completes
            if (audioRef.current) {
              audioRef.current.play().catch(e => console.error("Audio play failed:", e));
            }
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [isRunning, isPaused, timeLeft]);

  useEffect(() => {
    // Convert timeLeft back to hours, minutes, seconds for display
    if (!isRunning || isPaused) return;

    const h = Math.floor(timeLeft / 3600);
    const m = Math.floor((timeLeft % 3600) / 60);
    const s = timeLeft % 60;

    document.title = `${formatTime(h)}:${formatTime(m)}:${formatTime(s)} - Timer`;

    return () => {
      document.title = 'ToollyHub'; // Reset title when component unmounts or timer stops
    };
  }, [timeLeft, isRunning, isPaused]);

  const startTimer = () => {
    // Validate input
    if (hours === 0 && minutes === 0 && seconds === 0) {
      setError('Please set a time greater than zero');
      return;
    }

    setError('');
    const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
    setTimeLeft(totalSeconds);
    setInitialTime(totalSeconds);
    setIsRunning(true);
    setIsPaused(false);
    setShowCompletionMessage(false);
  };

  const pauseTimer = () => {
    setIsPaused(true);
    clearInterval(timerRef.current);
  };

  const resumeTimer = () => {
    setIsPaused(false);
  };

  const resetTimer = () => {
    clearInterval(timerRef.current);
    setIsRunning(false);
    setIsPaused(false);
    setShowCompletionMessage(false);
    setTimeLeft(0);
    setHours(0);
    setMinutes(0);
    setSeconds(0);
    setError('');
    document.title = 'ToollyHub';
  };

  const formatTime = (time) => {
    return time.toString().padStart(2, '0');
  };

  const calculateProgress = () => {
    if (initialTime === 0) return 0;
    return ((initialTime - timeLeft) / initialTime) * 100;
  };

  const displayTimeLeft = () => {
    const h = Math.floor(timeLeft / 3600);
    const m = Math.floor((timeLeft % 3600) / 60);
    const s = timeLeft % 60;

    return `${formatTime(h)}:${formatTime(m)}:${formatTime(s)}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Countdown Timer - Track Time to Your Events | ToollyHub"
        description="Create custom countdown timers for any event. Track time remaining to important dates, deadlines, or special occasions. Simple, customizable, and easy to use countdown tool."
        keywords="countdown timer, event countdown, deadline timer, countdown clock, time tracker, event timer, countdown calculator, time remaining calculator"
      />

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Countdown Timer</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Create custom countdown timers for any event. Track time remaining to important dates, deadlines, or special occasions.
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl mb-8">
          <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6">
            <h2 className="text-2xl font-bold text-white">Timer Setup</h2>
            <p className="text-orange-100 mt-1">Set your countdown duration and start tracking time</p>
          </div>

          <div className="p-6">
            {!isRunning ? (
              <div className="mb-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="hours" className="block text-sm font-medium text-gray-700 mb-2">Hours</label>
                    <input
                      type="number"
                      id="hours"
                      min="0"
                      max="23"
                      value={hours}
                      onChange={(e) => setHours(parseInt(e.target.value) || 0)}
                      className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-center text-lg font-semibold"
                    />
                  </div>
                  <div>
                    <label htmlFor="minutes" className="block text-sm font-medium text-gray-700 mb-2">Minutes</label>
                    <input
                      type="number"
                      id="minutes"
                      min="0"
                      max="59"
                      value={minutes}
                      onChange={(e) => setMinutes(parseInt(e.target.value) || 0)}
                      className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-center text-lg font-semibold"
                    />
                  </div>
                  <div>
                    <label htmlFor="seconds" className="block text-sm font-medium text-gray-700 mb-2">Seconds</label>
                    <input
                      type="number"
                      id="seconds"
                      min="0"
                      max="59"
                      value={seconds}
                      onChange={(e) => setSeconds(parseInt(e.target.value) || 0)}
                      className="w-full px-4 py-3 rounded-lg border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-center text-lg font-semibold"
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="mb-6">
                <div className="bg-gray-50 rounded-xl p-6 mb-6">
                  <div className="flex mb-4 items-center justify-between">
                    <div>
                      <span className="text-sm font-semibold inline-block py-2 px-3 uppercase rounded-full text-orange-600 bg-orange-100">
                        Progress
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="text-sm font-semibold inline-block text-orange-600">
                        {Math.round(calculateProgress())}%
                      </span>
                    </div>
                  </div>
                  <div className="overflow-hidden h-3 mb-6 text-xs flex rounded-full bg-orange-200">
                    <div
                      style={{ width: `${calculateProgress()}%` }}
                      className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-orange-500 to-orange-600 transition-all duration-500 rounded-full"
                    ></div>
                  </div>
                  <div className="text-center">
                    <span className="text-6xl font-bold text-gray-800 font-mono">
                      {displayTimeLeft()}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl">
                {error}
              </div>
            )}

            {showCompletionMessage && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 text-green-700 rounded-xl text-center">
                <div className="flex items-center justify-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="font-semibold">Time's up!</span>
                </div>
                <p>Your countdown has finished.</p>
              </div>
            )}

            <div className="flex justify-center space-x-4">
              {!isRunning ? (
                <button
                  onClick={startTimer}
                  className="px-8 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold rounded-lg hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                >
                  Start Timer
                </button>
              ) : (
                <>
                  {!isPaused ? (
                    <button
                      onClick={pauseTimer}
                      className="px-6 py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white font-semibold rounded-lg hover:from-yellow-600 hover:to-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                    >
                      Pause
                    </button>
                  ) : (
                    <button
                      onClick={resumeTimer}
                      className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                    >
                      Resume
                    </button>
                  )}
                  <button
                    onClick={resetTimer}
                    className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white font-semibold rounded-lg hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                  >
                    Reset
                  </button>
                </>
              )}
            </div>

            {/* Hidden audio element for timer completion sound */}
            <audio ref={audioRef}>
              <source src="https://assets.mixkit.co/sfx/preview/mixkit-alarm-digital-clock-beep-989.mp3" type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Why Use a Countdown Timer?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Perfect For</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-orange-500 mr-2">•</span>
                  Productivity sessions and Pomodoro technique
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Cooking and baking timers
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Exercise and workout intervals
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">•</span>
                  Study sessions and breaks
                </li>
                <li className="flex items-center">
                  <span className="text-red-500 mr-2">•</span>
                  Meeting and presentation timing
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Features</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  <span>Visual progress indicator</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  <span>Pause and resume functionality</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  <span>Audio notification when time is up</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  <span>Browser tab title updates with remaining time</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  <span>Easy reset and restart options</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountdownTimer;
