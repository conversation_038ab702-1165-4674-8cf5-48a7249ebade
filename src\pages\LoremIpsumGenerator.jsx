import React, { useState } from 'react';
import SEO from '../components/SEO';

const LoremIpsumGenerator = () => {
  const [paragraphs, setParagraphs] = useState(3);
  const [words, setWords] = useState(50);
  const [generatedText, setGeneratedText] = useState('');
  const [genType, setGenType] = useState('paragraphs');
  const [copied, setCopied] = useState(false);

  // Sample words for generating lorem ipsum
  const loremWords = [
    'lorem', 'ipsum', 'dolor', 'sit', 'amet', 'consectetur', 'adipiscing', 'elit',
    'sed', 'do', 'eiusmod', 'tempor', 'incididunt', 'ut', 'labore', 'et', 'dolore',
    'magna', 'aliqua', 'enim', 'ad', 'minim', 'veniam', 'quis', 'nostrud', 'exercitation',
    'ullamco', 'laboris', 'nisi', 'ut', 'aliquip', 'ex', 'ea', 'commodo', 'consequat',
    'duis', 'aute', 'irure', 'dolor', 'in', 'reprehenderit', 'voluptate', 'velit',
    'esse', 'cillum', 'dolore', 'eu', 'fugiat', 'nulla', 'pariatur', 'excepteur',
    'sint', 'occaecat', 'cupidatat', 'non', 'proident', 'sunt', 'in', 'culpa', 'qui',
    'officia', 'deserunt', 'mollit', 'anim', 'id', 'est', 'laborum'
  ];

  // Generate a random word from our array
  const getRandomWord = () => {
    return loremWords[Math.floor(Math.random() * loremWords.length)];
  };

  // Generate a sentence with a random number of words (min 5, max 15)
  const generateSentence = (minWords = 5, maxWords = 15) => {
    const numWords = Math.floor(Math.random() * (maxWords - minWords + 1)) + minWords;
    let sentence = getRandomWord().charAt(0).toUpperCase() + getRandomWord().slice(1);
    
    for (let i = 1; i < numWords; i++) {
      sentence += ' ' + getRandomWord();
    }
    
    return sentence + '.';
  };

  // Generate a paragraph with a random number of sentences (min 3, max 8)
  const generateParagraph = () => {
    const numSentences = Math.floor(Math.random() * 6) + 3;
    let paragraph = '';
    
    for (let i = 0; i < numSentences; i++) {
      paragraph += ' ' + generateSentence();
    }
    
    return paragraph.trim();
  };

  // Generate lorem ipsum text based on user parameters
  const generateLoremIpsum = () => {
    let result = '';
    
    if (genType === 'paragraphs') {
      for (let i = 0; i < paragraphs; i++) {
        result += generateParagraph() + '\n\n';
      }
    } else {
      let wordCount = 0;
      let text = '';
      
      while (wordCount < words) {
        const sentence = generateSentence();
        text += ' ' + sentence;
        wordCount += sentence.split(' ').length;
      }
      
      // Trim to exact word count
      const allWords = text.trim().split(' ');
      result = allWords.slice(0, words).join(' ');
    }
    
    setGeneratedText(result.trim());
  };

  // Copy text to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Lorem Ipsum Generator - Create Placeholder Text"
        description="Generate custom Lorem Ipsum placeholder text for your design projects. Choose paragraphs or word count for perfect filler text."
        keywords="lorem ipsum generator, placeholder text, dummy text, filler text, web design tool, content placeholder"
      />

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Lorem Ipsum Generator</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Generate customized placeholder text for your design projects
          </p>
        </div>
        
        <div className="grid grid-cols-1 gap-8">
          {/* Control Panel */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-6">
              <h2 className="text-2xl font-bold text-white">Generation Settings</h2>
              <p className="text-green-100 mt-1">Configure your Lorem Ipsum output</p>
            </div>
            
            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Generation Type
                  </label>
                  <div className="flex flex-wrap gap-4">
                    <div className="flex items-center">
                      <input
                        id="paragraphs"
                        type="radio"
                        checked={genType === 'paragraphs'}
                        onChange={() => setGenType('paragraphs')}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <label htmlFor="paragraphs" className="ml-2 text-gray-700">
                        By Paragraphs
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="words"
                        type="radio"
                        checked={genType === 'words'}
                        onChange={() => setGenType('words')}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <label htmlFor="words" className="ml-2 text-gray-700">
                        By Word Count
                      </label>
                    </div>
                  </div>
                </div>
                
                <div>
                  <label htmlFor="count" className="block text-sm font-medium text-gray-700 mb-2">
                    {genType === 'paragraphs' ? 'Number of Paragraphs' : 'Number of Words'}
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="number"
                      id="count"
                      value={genType === 'paragraphs' ? paragraphs : words}
                      onChange={(e) => {
                        const value = Math.max(1, parseInt(e.target.value) || 1);
                        if (genType === 'paragraphs') {
                          setParagraphs(Math.min(value, 50));
                        } else {
                          setWords(Math.min(value, 1000));
                        }
                      }}
                      min="1"
                      max={genType === 'paragraphs' ? '50' : '1000'}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500">
                      {genType === 'paragraphs' ? 'paragraphs' : 'words'}
                    </div>
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    {genType === 'paragraphs' 
                      ? 'Maximum 50 paragraphs' 
                      : 'Maximum 1000 words'}
                  </p>
                </div>
                
                <button
                  onClick={generateLoremIpsum}
                  className="w-full md:w-auto px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                  Generate Lorem Ipsum
                </button>
              </div>
            </div>
          </div>
          
          {/* Generated Text Output */}
          {generatedText && (
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 flex justify-between items-center">
                <div>
                  <h2 className="text-2xl font-bold text-white">Generated Text</h2>
                  <p className="text-blue-100 mt-1">
                    {genType === 'paragraphs' 
                      ? `${paragraphs} paragraph${paragraphs > 1 ? 's' : ''} of Lorem Ipsum` 
                      : `${words} word${words > 1 ? 's' : ''} of Lorem Ipsum`}
                  </p>
                </div>
                <button
                  onClick={copyToClipboard}
                  className="px-4 py-2 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 transition-colors flex items-center shadow-sm"
                >
                  {copied ? (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Copied!
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                        <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                      </svg>
                      Copy Text
                    </>
                  )}
                </button>
              </div>
              
              <div className="p-6">
                <div className="prose max-w-none whitespace-pre-wrap text-gray-700 bg-gray-50 p-6 rounded-xl border border-gray-100">
                  {generatedText.split('\n\n').map((paragraph, index) => (
                    <p key={index} className="mb-4 last:mb-0">{paragraph}</p>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">About Lorem Ipsum</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">What is Lorem Ipsum?</h3>
              <p className="text-gray-600">
                Lorem Ipsum is dummy text used in the design, printing, and typesetting industry. 
                It's been the standard dummy text since the 1500s when an unknown printer took a galley of 
                type and scrambled it to make a type specimen book.
              </p>
              
              <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">Why Use Placeholder Text?</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  <div>
                    <strong>Focus on Design:</strong> Evaluate layouts without being distracted by readable content
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <div>
                    <strong>Prototype Quickly:</strong> Test layouts before final content is available
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">•</span>
                  <div>
                    <strong>Balance Typography:</strong> Ensure proper spacing and visual hierarchy
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-pink-500 mr-2">•</span>
                  <div>
                    <strong>Client Presentations:</strong> Show realistic-looking mockups
                  </div>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">How to Use This Generator</h3>
              <ol className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <span className="flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-700 font-bold text-sm mr-2">1</span>
                  <span><strong>Choose your output type:</strong> Generate by paragraphs or specific word count</span>
                </li>
                <li className="flex items-start">
                  <span className="flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-700 font-bold text-sm mr-2">2</span>
                  <span><strong>Set the amount:</strong> Up to 50 paragraphs or 1000 words</span>
                </li>
                <li className="flex items-start">
                  <span className="flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-700 font-bold text-sm mr-2">3</span>
                  <span><strong>Generate:</strong> Click the button to create your Lorem Ipsum text</span>
                </li>
                <li className="flex items-start">
                  <span className="flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-700 font-bold text-sm mr-2">4</span>
                  <span><strong>Use the text:</strong> Copy with one click to paste into your project</span>
                </li>
              </ol>
              
              <div className="bg-blue-50 p-6 rounded-xl mt-8">
                <h3 className="text-lg font-semibold text-blue-700 mb-2">Pro Tip</h3>
                <p className="text-gray-700">
                  For web design projects, start with more Lorem Ipsum than you think you'll need, 
                  then trim it down as real content becomes available. This helps ensure your design 
                  accommodates varying content lengths and avoids layout surprises later.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoremIpsumGenerator;
