# 🎯 Understanding Percentages: A Modern Guide

<div style="font-family: 'Segoe UI', system-ui, -apple-system, sans-serif; color: #2c3e50; max-width: 1200px; margin: 0 auto; padding: 20px;">

<div style="background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%); padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
## 📊 What is a Percentage?

A percentage is a number expressed as a fraction of 100. The symbol "%" represents percentage. For example, 50% means 50 out of 100, or half of the total.

<div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin-top: 15px; border-left: 4px solid #3498db;">
💡 **Quick Tip**: Percentages are everywhere in daily life - from shopping discounts to test scores. Understanding them is key to making informed decisions!
</div>
</div>

## 🔢 Common Percentage Calculations

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">

<div style="background: #ffffff; padding: 25px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); border-top: 4px solid #3498db;">
### 1️⃣ Finding a Percentage of a Number
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px;">
**Example**: What is 20% of 150?

**Formula**: Number × (Percentage ÷ 100)
```math
150 × (20 ÷ 100) = 150 × 0.2 = 30
```
</div>
</div>

<div style="background: #ffffff; padding: 25px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); border-top: 4px solid #2ecc71;">
### 2️⃣ Finding What Percentage One Number is of Another
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px;">
**Example**: 30 is what percent of 150?

**Formula**: (First Number ÷ Second Number) × 100
```math
(30 ÷ 150) × 100 = 0.2 × 100 = 20%
```
</div>
</div>

<div style="background: #ffffff; padding: 25px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); border-top: 4px solid #e74c3c;">
### 3️⃣ Percentage Change
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px;">
**Example**: Change from 100 to 120?

**Formula**: ((New - Old) ÷ Old) × 100
```math
((120 - 100) ÷ 100) × 100 = 20% increase
```
</div>
</div>

<div style="background: #ffffff; padding: 25px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); border-top: 4px solid #f1c40f;">
### 4️⃣ Calculating Discounts
<div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px;">
**Example**: $100 with 25% discount

**Formula**: Original - (Original × Discount)
```math
$100 - ($100 × 0.25) = $75
```
</div>
</div>

</div>

## 🌍 Real-World Applications

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin: 30px 0;">

<div style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%); padding: 20px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);">
### 🛍️ Shopping
- Calculating sale prices
- Comparing discounts
- Understanding tax rates
</div>

<div style="background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%); padding: 20px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);">
### 💰 Finance
- Interest rates
- Investment returns
- Loan calculations
</div>

<div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 20px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);">
### 📈 Business
- Profit margins
- Growth rates
- Market share analysis
</div>

<div style="background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%); padding: 20px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);">
### 📚 Education
- Grade calculations
- Test scores
- Progress tracking
</div>

<div style="background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%); padding: 20px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);">
### 🏥 Health
- Weight changes
- Nutritional information
- Medical statistics
</div>

<div style="background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%); padding: 20px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);">
### 📊 Statistics
- Trend analysis
- Proportional relationships
- Data interpretation
</div>

</div>

<div style="background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%); padding: 25px; border-radius: 12px; margin-top: 30px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
## 💡 Pro Tips
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 15px;">
<div style="background: #ffffff; padding: 15px; border-radius: 8px;">
🔍 Always double-check your calculations
</div>
<div style="background: #ffffff; padding: 15px; border-radius: 8px;">
🧮 Use a calculator for complex percentages
</div>
<div style="background: #ffffff; padding: 15px; border-radius: 8px;">
📈 Remember percentages can exceed 100%
</div>
<div style="background: #ffffff; padding: 15px; border-radius: 8px;">
⚠️ Distinguish between percentage points and percentages
</div>
</div>
</div>

</div>

<style>
h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 0.5em;
    text-align: center;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.3em;
}

h2 {
    color: #34495e;
    font-size: 1.8em;
    margin-top: 1.5em;
    margin-bottom: 0.8em;
}

h3 {
    color: #2980b9;
    font-size: 1.4em;
    margin-top: 1em;
    margin-bottom: 0.6em;
}

strong {
    color: #e74c3c;
    font-weight: 600;
}

code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: #2c3e50;
}

.math {
    font-family: 'Consolas', 'Monaco', monospace;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    display: block;
    margin: 8px 0;
}

@media (max-width: 768px) {
    div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
}
</style> 