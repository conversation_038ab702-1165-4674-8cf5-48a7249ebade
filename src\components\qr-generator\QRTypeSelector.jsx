import React from 'react';
import { QR_TYPES } from './utils/constants';

const QRTypeSelector = ({ qrType, setQrType }) => {
  return (
    <div className="mb-6">
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {Object.entries(QR_TYPES).map(([type, config]) => (
            <button
              key={type}
              onClick={() => setQrType(type)}
              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                qrType === type
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{config.icon}</span>
              {config.label}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default QRTypeSelector;
