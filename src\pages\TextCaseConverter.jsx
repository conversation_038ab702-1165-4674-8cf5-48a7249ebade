import React, { useState } from 'react';
import SEO from '../components/SEO';

const TextCaseConverter = () => {
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [activeConversion, setActiveConversion] = useState('');
  const [copied, setCopied] = useState(false);

  // Case conversion functions
  const conversions = {
    uppercase: (text) => text.toUpperCase(),
    lowercase: (text) => text.toLowerCase(),
    titlecase: (text) => {
      return text.replace(/\w\S*/g, (txt) => {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
      });
    },
    sentencecase: (text) => {
      return text.toLowerCase().replace(/(^\s*\w|[.!?]\s*\w)/g, (txt) => {
        return txt.toUpperCase();
      });
    },
    alternatingcase: (text) => {
      return text.split('').map((char, i) => 
        i % 2 === 0 ? char.toLowerCase() : char.toUpperCase()
      ).join('');
    },
    camelcase: (text) => {
      return text.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+(.)/g, (match, chr) => chr.toUpperCase())
        .replace(/^\w/, (chr) => chr.toLowerCase());
    },
    pascalcase: (text) => {
      return text.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+(.)/g, (match, chr) => chr.toUpperCase())
        .replace(/^\w/, (chr) => chr.toUpperCase());
    },
    snakecase: (text) => {
      return text.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+/g, '_');
    },
    kebabcase: (text) => {
      return text.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+/g, '-');
    },
    inversecase: (text) => {
      return text.split('').map(char => {
        if (char === char.toUpperCase()) {
          return char.toLowerCase();
        }
        return char.toUpperCase();
      }).join('');
    }
  };

  // Apply selected conversion
  const convertText = (conversionType) => {
    if (!inputText.trim()) {
      setOutputText('');
      setActiveConversion('');
      return;
    }

    const convertedText = conversions[conversionType](inputText);
    setOutputText(convertedText);
    setActiveConversion(conversionType);
  };

  // Copy text to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(outputText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Clear both input and output
  const clearText = () => {
    setInputText('');
    setOutputText('');
    setActiveConversion('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Text Case Converter - Change Text Case Easily"
        description="Free online text case converter. Convert text to UPPERCASE, lowercase, Title Case, Sentence case, and more. Perfect for formatting text for various needs."
        keywords="text case converter, uppercase, lowercase, title case, sentence case, camel case, snake case, change text case, text formatting"
      />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Text Case Converter</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Transform your text into different cases with a single click. Perfect for formatting text for various purposes.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-3">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl mb-6">
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6">
                <h2 className="text-2xl font-bold text-white">Enter Your Text</h2>
                <p className="text-purple-100 mt-1">Type or paste your text below to convert it</p>
              </div>
              
              <div className="p-6">
                <div className="flex justify-end mb-2">
                  <button
                    onClick={clearText}
                    className="px-4 py-1 text-sm text-purple-600 bg-purple-50 hover:bg-purple-100 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
                    disabled={!inputText}
                  >
                    Clear
                  </button>
                </div>
                <textarea
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  className="w-full h-40 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Type or paste your text here..."
                />
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl h-full">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4">
                <h2 className="text-xl font-bold text-white">Case Options</h2>
                <p className="text-blue-100 text-sm">Select a conversion method</p>
              </div>
              
              <div className="p-4">
                <div className="grid grid-cols-1 gap-2">
                  {Object.keys(conversions).map((type) => (
                    <button
                      key={type}
                      onClick={() => convertText(type)}
                      className={`px-4 py-2 rounded-lg text-left transition-colors ${
                        activeConversion === type
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-50 hover:bg-gray-100 text-gray-700'
                      }`}
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl h-full">
              <div className="bg-gradient-to-r from-green-500 to-green-600 p-6">
                <h2 className="text-2xl font-bold text-white">Converted Result</h2>
                <p className="text-green-100 mt-1">
                  {activeConversion && (
                    `Text in ${activeConversion.charAt(0).toUpperCase() + activeConversion.slice(1)} format`
                  )}
                </p>
              </div>
              
              <div className="p-6">
                {outputText ? (
                  <div className="space-y-4">
                    <div className="flex justify-end">
                      <button
                        onClick={copyToClipboard}
                        className="flex items-center px-4 py-2 text-sm text-green-600 bg-green-50 hover:bg-green-100 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
                      >
                        {copied ? (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            Copied!
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                              <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                            </svg>
                            Copy
                          </>
                        )}
                      </button>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-xl border border-gray-100 min-h-[150px] whitespace-pre-wrap">
                      {outputText}
                    </div>
                  </div>
                ) : (
                  <div className="p-6 text-center text-gray-500">
                    <p>Your converted text will appear here</p>
                    <p className="text-sm mt-2">Select a case option and enter some text to get started</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Why Use a Text Case Converter?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Common Applications</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Content creation for articles and social media
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Web development naming conventions
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">•</span>
                  SEO optimization for titles and meta descriptions
                </li>
                <li className="flex items-center">
                  <span className="text-orange-500 mr-2">•</span>
                  Data normalization for databases
                </li>
                <li className="flex items-center">
                  <span className="text-red-500 mr-2">•</span>
                  Maintaining consistent branding style
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Available Text Cases</h3>
              <div className="space-y-2 text-gray-600">
                <p><span className="font-semibold">UPPERCASE:</span> ALL CHARACTERS CAPITALIZED</p>
                <p><span className="font-semibold">lowercase:</span> all characters in small letters</p>
                <p><span className="font-semibold">Title Case:</span> First Letter Of Each Word Capitalized</p>
                <p><span className="font-semibold">Sentence case:</span> First letter of each sentence capitalized</p>
                <p><span className="font-semibold">camelCase:</span> firstLetterLowerCaseThenCapitalized</p>
                <p><span className="font-semibold">PascalCase:</span> FirstLetterOfEachWordCapitalized</p>
                <p><span className="font-semibold">snake_case:</span> words_separated_by_underscores</p>
                <p><span className="font-semibold">kebab-case:</span> words-separated-by-hyphens</p>
              </div>
            </div>
          </div>
          
          <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">How to Use Our Text Case Converter</h3>
          <ol className="space-y-2 text-gray-600">
            <li className="flex items-start">
              <span className="text-purple-500 mr-2">1.</span>
              <span>Type or paste your text in the input box</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-500 mr-2">2.</span>
              <span>Select one of the case conversion options from the sidebar</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-500 mr-2">3.</span>
              <span>View your instantly converted text in the result box</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-500 mr-2">4.</span>
              <span>Copy the result with a single click using the copy button</span>
            </li>
          </ol>
          
          <p className="text-gray-600 mt-4">
            Our text case converter processes all conversions instantly in your browser, ensuring your text remains private and secure.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TextCaseConverter;
