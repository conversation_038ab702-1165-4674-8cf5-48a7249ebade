import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const DaysUntilCalculator = () => {
  const [customDate, setCustomDate] = useState('');
  const [customLabel, setCustomLabel] = useState('');
  const [daysUntil, setDaysUntil] = useState({});
  const [customEvents, setCustomEvents] = useState([]);
  
  // Common events - can be expanded
  const commonEvents = [
    { name: "Christmas", date: () => {
      const currentYear = new Date().getFullYear();
      return `${currentYear}-12-25`;
    }},
    { name: "New Year", date: () => {
      const nextYear = new Date().getFullYear() + 1;
      return `${nextYear}-01-01`;
    }},
    { name: "Valentine's Day", date: () => {
      const currentYear = new Date().getFullYear();
      return `${currentYear}-02-14`;
    }},
    { name: "Halloween", date: () => {
      const currentYear = new Date().getFullYear();
      return `${currentYear}-10-31`;
    }},
    { name: "2026", date: () => "2026-01-01" },
    { name: "2030", date: () => "2030-01-01" }
  ];

  // Function to calculate days until a given date
  const calculateDaysUntil = (targetDate, label) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const target = new Date(targetDate);
    target.setHours(0, 0, 0, 0);
    
    // Handle dates in the past by setting to next year
    if (target < today && !targetDate.includes('-')) {
      const [month, day] = targetDate.split('-');
      target.setFullYear(today.getFullYear() + 1);
    }
    
    const differenceInTime = target - today;
    const differenceInDays = Math.ceil(differenceInTime / (1000 * 3600 * 24));
    
    return {
      label: label,
      date: target.toLocaleDateString(),
      days: differenceInDays
    };
  };

  // Calculate days for common events
  useEffect(() => {
    const results = {};
    commonEvents.forEach(event => {
      const targetDate = event.date();
      results[event.name] = calculateDaysUntil(targetDate, event.name);
    });
    setDaysUntil(results);
  }, []);

  // Handle custom date submission
  const handleCustomDateSubmit = (e) => {
    e.preventDefault();
    if (!customDate) return;
    
    const newEvent = {
      label: customLabel || `Custom Event (${customDate})`,
      result: calculateDaysUntil(customDate, customLabel || 'Custom Event')
    };
    
    setCustomEvents([...customEvents, newEvent]);
    setCustomDate('');
    setCustomLabel('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO 
        title="Days Until Calculator - Calculate Days Between Dates"
        description="Find out exactly how many days until Christmas, New Year, your birthday, or any custom date with our free days until calculator."
        keywords="days until calculator, days between dates, countdown calculator, days until Christmas, days until New Year, date difference calculator"
      />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Days Until Calculator</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find out exactly how many days until your important events and special occasions
          </p>
        </div>
        
        <div className="grid grid-cols-1 gap-8">
          {/* Custom date input */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6">
              <h2 className="text-2xl font-bold text-white">Add Custom Event</h2>
              <p className="text-indigo-100 mt-1">Calculate days until your important date</p>
            </div>
            
            <div className="p-6">
              <form onSubmit={handleCustomDateSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="customLabel" className="block text-sm font-medium text-gray-700 mb-2">
                      Event Name (Optional)
                    </label>
                    <input
                      type="text"
                      id="customLabel"
                      value={customLabel}
                      onChange={(e) => setCustomLabel(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="My Birthday, Vacation, etc."
                    />
                  </div>
                  <div>
                    <label htmlFor="customDate" className="block text-sm font-medium text-gray-700 mb-2">
                      Target Date
                    </label>
                    <input
                      type="date"
                      id="customDate"
                      value={customDate}
                      onChange={(e) => setCustomDate(e.target.value)}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                </div>
                <div>
                  <button
                    type="submit"
                    className="px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors flex items-center justify-center shadow-md"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                    </svg>
                    Calculate Days
                  </button>
                </div>
              </form>
            </div>
          </div>
          
          {/* Common events */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6">
              <h2 className="text-2xl font-bold text-white">Common Events</h2>
              <p className="text-purple-100 mt-1">Countdown to popular dates and holidays</p>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(daysUntil).map(([key, value]) => (
                  <div key={key} className="bg-gray-50 p-5 rounded-xl border border-gray-200 transition-all hover:shadow-md hover:border-purple-200">
                    <h3 className="font-medium text-lg text-gray-800">{value.label}</h3>
                    <p className="text-gray-600 text-sm">{value.date}</p>
                    <div className="flex items-center mt-3">
                      <div className="w-2 h-2 rounded-full mr-2" 
                        style={{ backgroundColor: value.days < 0 ? '#f87171' : value.days === 0 ? '#10b981' : '#6366f1' }}>
                      </div>
                      <p className={`font-bold text-xl ${
                        value.days < 0 
                          ? 'text-red-500' 
                          : value.days === 0 
                            ? 'text-green-600' 
                            : 'text-indigo-600'
                      }`}>
                        {value.days < 0 
                          ? `${Math.abs(value.days)} days ago` 
                          : value.days === 0 
                            ? "Today!" 
                            : `${value.days} days`}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* Custom events */}
          {customEvents.length > 0 && (
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
                <h2 className="text-2xl font-bold text-white">Your Custom Events</h2>
                <p className="text-blue-100 mt-1">Tracking {customEvents.length} custom date{customEvents.length !== 1 ? 's' : ''}</p>
              </div>
              
              <div className="p-6">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Event
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Target Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Days Until
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {customEvents.map((event, index) => (
                        <tr key={index} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors`}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {event.label}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {event.result.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                              event.result.days < 0 
                                ? 'bg-red-100 text-red-800' 
                                : event.result.days === 0 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-blue-100 text-blue-800'
                            }`}>
                              {event.result.days < 0 
                                ? `${Math.abs(event.result.days)} days ago` 
                                : event.result.days === 0 
                                  ? "Today!" 
                                  : `${event.result.days} days`}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">About Days Until Calculator</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">How to Use This Calculator</h3>
              <ol className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <span className="flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-700 font-bold text-sm mr-2">1</span>
                  <span><strong>View common events</strong> to see countdowns for holidays and milestones</span>
                </li>
                <li className="flex items-start">
                  <span className="flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-700 font-bold text-sm mr-2">2</span>
                  <span><strong>Enter your event name</strong> (optional) to personalize your countdown</span>
                </li>
                <li className="flex items-start">
                  <span className="flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-700 font-bold text-sm mr-2">3</span>
                  <span><strong>Select a target date</strong> using the date picker</span>
                </li>
                <li className="flex items-start">
                  <span className="flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-700 font-bold text-sm mr-2">4</span>
                  <span><strong>Click "Calculate Days"</strong> to get your countdown result</span>
                </li>
              </ol>
              
              <div className="bg-blue-50 p-6 rounded-xl mt-8">
                <h3 className="text-lg font-semibold text-blue-700 mb-2">Pro Tip</h3>
                <p className="text-gray-700">
                  Use this calculator to keep track of multiple important dates at once. 
                  Add all your key events and deadlines to have a single dashboard 
                  showing exactly how much time you have left for each one.
                </p>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Why Use a Days Until Calculator?</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  <div>
                    <strong>Plan Ahead:</strong> Know exactly how much time you have before deadlines and events
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <div>
                    <strong>Build Excitement:</strong> Count down to vacations, holidays, and special occasions
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">•</span>
                  <div>
                    <strong>Stay Organized:</strong> Track multiple deadlines and never miss an important date
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-pink-500 mr-2">•</span>
                  <div>
                    <strong>Time Management:</strong> Visualize how quickly upcoming events are approaching
                  </div>
                </li>
              </ul>
              
              <h3 className="text-xl font-semibold text-gray-700 mt-8 mb-4">Applications</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  <div>Personal events: birthdays, anniversaries, retirements</div>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-500 mr-2">•</span>
                  <div>Project deadlines: work milestones, submissions, launches</div>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">•</span>
                  <div>Travel: vacations, trips, flight departures</div>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  <div>Education: exam dates, application deadlines, graduations</div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DaysUntilCalculator;
