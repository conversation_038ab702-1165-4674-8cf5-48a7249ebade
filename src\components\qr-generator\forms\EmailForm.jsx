import React from 'react';
import { INPUT_STYLES, LABEL_STYLES } from '../utils/constants';

const EmailForm = ({ formData, updateFormData }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Email Address</label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => updateFormData('email', e.target.value)}
          className={INPUT_STYLES}
          placeholder="<EMAIL>"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Subject</label>
        <input
          type="text"
          value={formData.subject}
          onChange={(e) => updateFormData('subject', e.target.value)}
          className={INPUT_STYLES}
          placeholder="Email subject"
        />
      </div>
      <div className="space-y-2 sm:col-span-2">
        <label className={LABEL_STYLES}>Message Body</label>
        <textarea
          value={formData.body}
          onChange={(e) => updateFormData('body', e.target.value)}
          className={INPUT_STYLES}
          placeholder="Email message..."
          rows="3"
        />
      </div>
    </div>
  );
};

export default EmailForm;
