/**
 * Generate QR data string based on type and form data
 */
export const generateQRData = (qrType, formData) => {
  const data = formData[qrType];

  switch (qrType) {
    case 'text':
      return data.content;

    case 'url':
      return data.url.startsWith('http') ? data.url : `https://${data.url}`;

    case 'email':
      return `mailto:${data.email}?subject=${encodeURIComponent(data.subject)}&body=${encodeURIComponent(data.body)}`;

    case 'phone':
      return `tel:${data.number}`;

    case 'sms':
      return `sms:${data.number}?body=${encodeURIComponent(data.message)}`;

    case 'wifi':
      return `WIFI:T:${data.security};S:${data.ssid};P:${data.password};H:${data.hidden ? 'true' : 'false'};;`;

    case 'vcard':
      return `BEGIN:VCARD
VERSION:3.0
FN:${data.firstName} ${data.lastName}
ORG:${data.organization}
TEL:${data.phone}
EMAIL:${data.email}
URL:${data.website}
ADR:;;${data.address};;;;
END:VCARD`;

    case 'event':
      const startDateTime = `${data.startDate}T${data.startTime}:00`;
      const endDateTime = `${data.endDate}T${data.endTime}:00`;
      return `BEGIN:VEVENT
SUMMARY:${data.title}
LOCATION:${data.location}
DESCRIPTION:${data.description}
DTSTART:${startDateTime.replace(/[-:]/g, '')}
DTEND:${endDateTime.replace(/[-:]/g, '')}
END:VEVENT`;

    default:
      return '';
  }
};

/**
 * Download QR code as PNG file
 */
export const downloadQRCode = (qrDataURL, qrType) => {
  if (!qrDataURL) return;

  const link = document.createElement('a');
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
  link.download = `qr-${qrType}-${timestamp}.png`;
  link.href = qrDataURL;
  link.click();
};

/**
 * Copy QR code image to clipboard
 */
export const copyQRToClipboard = async (canvas, onSuccess, onError) => {
  if (!canvas) return;

  try {
    canvas.toBlob(async (blob) => {
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      onSuccess?.();
    });
  } catch (error) {
    console.error('Failed to copy QR code:', error);
    onError?.(error);
  }
};

/**
 * Copy QR data text to clipboard
 */
export const copyDataToClipboard = async (data, onSuccess, onError) => {
  if (!data) return;

  try {
    await navigator.clipboard.writeText(data);
    onSuccess?.();
  } catch (error) {
    console.error('Failed to copy data:', error);
    onError?.(error);
  }
};
