import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const DateDifferenceCalculator = () => {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  // Set default to current date on component mount
  useEffect(() => {
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    setEndDate(formattedDate);
  }, []);

  const calculateDifference = (e) => {
    e.preventDefault();
    
    if (!startDate || !endDate) {
      setError('Please select both start and end dates');
      setResult(null);
      return;
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Validate dates
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      setError('Please enter valid dates');
      setResult(null);
      return;
    }
    
    setError('');
    
    // Calculate difference in milliseconds
    const diffMs = end - start;
    
    // Handle negative difference (end date before start date)
    const isNegative = diffMs < 0;
    const absDiffMs = Math.abs(diffMs);
    
    // Calculate different units
    const diffDays = Math.floor(absDiffMs / (1000 * 60 * 60 * 24));
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = calculateMonthDifference(start, end);
    const diffYears = calculateYearDifference(start, end);
    
    // Format for human-readable display
    const { years, months, days } = calculatePreciseDifference(start, end);
    
    setResult({
      days: diffDays,
      weeks: diffWeeks,
      months: diffMonths,
      years: diffYears,
      precise: { years, months, days },
      isNegative
    });
  };

  // Calculate month difference accounting for partial months
  const calculateMonthDifference = (start, end) => {
    const isNegative = end < start;
    if (isNegative) {
      [start, end] = [end, start]; // Swap dates if negative
    }
    
    let months = (end.getFullYear() - start.getFullYear()) * 12;
    months -= start.getMonth();
    months += end.getMonth();
    
    // Adjust for day of month
    if (end.getDate() < start.getDate()) {
      months--;
    }
    
    return months;
  };

  // Calculate year difference accounting for partial years
  const calculateYearDifference = (start, end) => {
    const isNegative = end < start;
    if (isNegative) {
      [start, end] = [end, start]; // Swap dates if negative
    }
    
    let years = end.getFullYear() - start.getFullYear();
    
    // Adjust if we haven't reached the anniversary month/day yet
    if (
      end.getMonth() < start.getMonth() ||
      (end.getMonth() === start.getMonth() && end.getDate() < start.getDate())
    ) {
      years--;
    }
    
    return years;
  };

  // Calculate precise difference in years, months, and days
  const calculatePreciseDifference = (start, end) => {
    const isNegative = end < start;
    if (isNegative) {
      [start, end] = [end, start]; // Swap dates if negative
    }
    
    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();
    
    // Adjust for negative days
    if (days < 0) {
      // Get days in the previous month
      const previousMonth = new Date(end.getFullYear(), end.getMonth(), 0);
      days += previousMonth.getDate();
      months--;
    }
    
    // Adjust for negative months
    if (months < 0) {
      months += 12;
      years--;
    }
    
    return { years, months, days };
  };

  // Swap start and end dates
  const swapDates = () => {
    const temp = startDate;
    setStartDate(endDate);
    setEndDate(temp);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <SEO
        title="Date Difference Calculator - Find Days Between Dates"
        description="Calculate the exact duration between two dates with our Date Difference Calculator. Find results in days, weeks, months, and years."
        keywords="date calculator, days between dates, date difference, date duration, calculate dates, time between dates"
      />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Date Difference Calculator</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Calculate the exact time between any two dates. Perfect for project planning, age calculation, and event countdowns.
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl mb-8">
          <div className="bg-gradient-to-r from-teal-500 to-teal-600 p-6">
            <h2 className="text-2xl font-bold text-white">Calculate Date Difference</h2>
            <p className="text-teal-100 mt-1">Find the exact duration between two dates</p>
          </div>
          
          <div className="p-6">
            <form onSubmit={calculateDifference} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="start-date">
                    Start Date
                  </label>
                  <input
                    id="start-date"
                    type="date"
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-teal-500 focus:ring-teal-500"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="end-date">
                    End Date
                  </label>
                  <input
                    id="end-date"
                    type="date"
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-teal-500 focus:ring-teal-500"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    required
                  />
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  type="submit"
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-teal-500 to-teal-600 text-white font-semibold rounded-lg hover:from-teal-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                >
                  Calculate Difference
                </button>
                
                <button
                  type="button"
                  onClick={swapDates}
                  className="px-6 py-3 bg-white text-teal-600 font-semibold rounded-lg border border-teal-200 hover:bg-teal-50 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2"
                >
                  Swap Dates
                </button>
              </div>
              
              {error && (
                <div className="p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
                  {error}
                </div>
              )}
            </form>
          </div>
        </div>

        {/* Results Section */}
        {result && (
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
              <h2 className="text-2xl font-bold text-white">Date Difference Results</h2>
              <p className="text-blue-100 mt-1">
                {result.isNegative ? 'First date is after second date' : ''}
              </p>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 bg-gray-50 rounded-xl border border-gray-100">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">Time Units</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">Days:</span>
                      <span className="font-medium">{result.days.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">Weeks:</span>
                      <span className="font-medium">{result.weeks.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">Months:</span>
                      <span className="font-medium">{result.months.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">Years:</span>
                      <span className="font-medium">{result.years.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-gray-50 rounded-xl border border-gray-100">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">Precise Duration</h3>
                  <div className="flex items-center justify-center h-full">
                    <p className="text-xl font-bold text-gray-800 text-center">
                      {result.precise.years > 0 && `${result.precise.years} ${result.precise.years === 1 ? 'year' : 'years'} `}
                      {result.precise.months > 0 && `${result.precise.months} ${result.precise.months === 1 ? 'month' : 'months'} `}
                      {result.precise.days > 0 && `${result.precise.days} ${result.precise.days === 1 ? 'day' : 'days'}`}
                      {result.precise.years === 0 && result.precise.months === 0 && result.precise.days === 0 ? '0 days' : ''}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-100">
                <div className="flex justify-between items-center">
                  <span className="font-semibold text-blue-800">Working Days (Excluding Weekends):</span>
                  <span className="font-medium text-blue-800">~{Math.round(result.days * 5/7).toLocaleString()} days</span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  Estimation based on a standard 5-day work week, not accounting for holidays.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* SEO Content */}
        <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Why Calculate Date Differences?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">Common Applications</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-teal-500 mr-2">•</span>
                  Project management and deadline tracking
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  Age calculation in years, months, and days
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">•</span>
                  Event planning and countdowns
                </li>
                <li className="flex items-center">
                  <span className="text-orange-500 mr-2">•</span>
                  Contract duration and service periods
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-4">How to Use This Calculator</h3>
              <ol className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-teal-500 mr-2">1.</span>
                  <span>Select a start date using the date picker</span>
                </li>
                <li className="flex items-start">
                  <span className="text-teal-500 mr-2">2.</span>
                  <span>Select an end date (defaults to today)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-teal-500 mr-2">3.</span>
                  <span>Click "Calculate Difference" to see results</span>
                </li>
                <li className="flex items-start">
                  <span className="text-teal-500 mr-2">4.</span>
                  <span>Use "Swap Dates" to reverse the calculation</span>
                </li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateDifferenceCalculator;
