import { useState } from 'react';
import { INITIAL_FORM_DATA } from '../utils/constants';

/**
 * Custom hook for managing form data across different QR types
 */
export const useFormData = () => {
  const [formData, setFormData] = useState(INITIAL_FORM_DATA);

  /**
   * Update form data for the current QR type
   */
  const updateFormData = (qrType, field, value) => {
    setFormData(prev => ({
      ...prev,
      [qrType]: {
        ...prev[qrType],
        [field]: value
      }
    }));
  };

  /**
   * Reset form data to initial values
   */
  const resetFormData = () => {
    setFormData(INITIAL_FORM_DATA);
  };

  /**
   * Get form data for a specific QR type
   */
  const getFormData = (qrType) => {
    return formData[qrType] || {};
  };

  /**
   * Set complete form data for a specific QR type
   */
  const setFormDataForType = (qrType, data) => {
    setFormData(prev => ({
      ...prev,
      [qrType]: data
    }));
  };

  return {
    formData,
    updateFormData,
    resetFormData,
    getFormData,
    setFormDataForType
  };
};
