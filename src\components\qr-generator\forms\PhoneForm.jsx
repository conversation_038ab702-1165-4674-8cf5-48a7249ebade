import React from 'react';
import { INPUT_STYLES, LABEL_STYLES } from '../utils/constants';

const PhoneForm = ({ formData, updateFormData }) => {
  return (
    <div className="space-y-2">
      <label className={LABEL_STYLES}>Phone Number</label>
      <input
        type="tel"
        value={formData.number}
        onChange={(e) => updateFormData('number', e.target.value)}
        className={INPUT_STYLES}
        placeholder="+1234567890"
      />
    </div>
  );
};

export default PhoneForm;
