// QR Code types configuration
export const QR_TYPES = {
  text: { label: 'Text', icon: '📝' },
  url: { label: 'URL', icon: '🔗' },
  email: { label: 'Email', icon: '📧' },
  phone: { label: 'Phone', icon: '📞' },
  sms: { label: 'SMS', icon: '💬' },
  wifi: { label: 'WiFi', icon: '📶' },
  vcard: { label: 'Contact', icon: '👤' },
  event: { label: 'Event', icon: '📅' }
};

// Default QR options
export const DEFAULT_QR_OPTIONS = {
  size: 256,
  margin: 4,
  color: {
    dark: '#000000',
    light: '#FFFFFF'
  },
  errorCorrectionLevel: 'M'
};

// Initial form data for all QR types
export const INITIAL_FORM_DATA = {
  text: { content: 'Hello World! This is a sample QR code.' },
  url: { url: 'https://example.com' },
  email: { email: '<EMAIL>', subject: 'Hello', body: 'Sample message' },
  phone: { number: '+1234567890' },
  sms: { number: '+1234567890', message: 'Hello from QR code!' },
  wifi: { ssid: 'MyWiFi', password: 'password123', security: 'WPA', hidden: false },
  vcard: {
    firstName: 'John', 
    lastName: 'Doe', 
    organization: 'Company Inc',
    phone: '+1234567890', 
    email: '<EMAIL>', 
    website: 'https://company.com',
    address: '123 Main St, City, State'
  },
  event: {
    title: 'Sample Event', 
    location: 'Conference Room', 
    description: 'Important meeting',
    startDate: '2024-12-01', 
    endDate: '2024-12-01', 
    startTime: '10:00', 
    endTime: '11:00'
  }
};

// Input field styles
export const INPUT_STYLES = "w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition";

// Label styles
export const LABEL_STYLES = "block text-sm font-medium text-gray-700";

// Button styles
export const BUTTON_STYLES = {
  primary: "px-6 py-3 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white font-semibold rounded-lg hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]",
  secondary: "px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]",
  small: "px-4 py-3 text-white rounded-lg transition-colors font-medium",
  download: "px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 font-semibold shadow-lg transform hover:scale-105"
};
