import React from 'react';
import { Link } from 'react-router-dom';

const ToolCard = ({ title, description, icon, linkTo }) => {
  return (
    <Link
      to={linkTo}
      className="block p-6 bg-white border border-gray-200 rounded-lg shadow-md hover:shadow-xl hover:scale-105 transform transition-transform duration-200 ease-in-out group"
    >
      <div className="flex flex-col items-start h-full"> {/* Ensure consistent height for items if needed */}
        {icon && (
          <div className="text-4xl mb-4 text-indigo-600 group-hover:text-indigo-700 transition-colors duration-200">
            {icon} {/* For now, using emoji or text character as icon */}
          </div>
        )}
        <h5 className="mb-2 text-2xl font-bold tracking-tight text-gray-900 group-hover:text-indigo-700 transition-colors duration-200">
          {title}
        </h5>
        <p className="font-normal text-gray-700 text-base">
          {description}
        </p>
      </div>
    </Link>
  );
};

export default ToolCard;
