import React from 'react';
import { INPUT_STYLES, LABEL_STYLES } from '../utils/constants';

const WiFiForm = ({ formData, updateFormData }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Network Name (SSID)</label>
        <input
          type="text"
          value={formData.ssid}
          onChange={(e) => updateFormData('ssid', e.target.value)}
          className={INPUT_STYLES}
          placeholder="WiFi Network Name"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Password</label>
        <input
          type="password"
          value={formData.password}
          onChange={(e) => updateFormData('password', e.target.value)}
          className={INPUT_STYLES}
          placeholder="WiFi Password"
        />
      </div>
      <div className="space-y-2">
        <label className={LABEL_STYLES}>Security Type</label>
        <select
          value={formData.security}
          onChange={(e) => updateFormData('security', e.target.value)}
          className={INPUT_STYLES}
        >
          <option value="WPA">WPA/WPA2</option>
          <option value="WEP">WEP</option>
          <option value="nopass">No Password</option>
        </select>
      </div>
      <div className="space-y-2 flex items-center">
        <label className="inline-flex items-center">
          <input
            type="checkbox"
            checked={formData.hidden}
            onChange={(e) => updateFormData('hidden', e.target.checked)}
            className="form-checkbox h-4 w-4 text-indigo-600"
          />
          <span className="ml-2 text-sm text-gray-700">Hidden Network</span>
        </label>
      </div>
    </div>
  );
};

export default WiFiForm;
