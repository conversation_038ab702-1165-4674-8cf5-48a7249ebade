import React, { useState, useRef } from 'react';
import SEO from '../components/SEO';

const BackgroundRemover = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [processedImageUrl, setProcessedImageUrl] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [tolerance, setTolerance] = useState(30);
  const [selectedColor, setSelectedColor] = useState('#ffffff');
  const [originalDimensions, setOriginalDimensions] = useState({ width: 0, height: 0 });
  const fileInputRef = useRef(null);
  const canvasRef = useRef(null);

  const backgroundPresets = [
    { name: 'White', color: '#ffffff' },
    { name: 'Black', color: '#000000' },
    { name: 'Green Screen', color: '#00ff00' },
    { name: 'Blue Screen', color: '#0000ff' },
    { name: 'Red', color: '#ff0000' },
    { name: 'Gray', color: '#808080' },
  ];

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setOriginalDimensions({ width: img.width, height: img.height });
          setImagePreview(e.target.result);
          setProcessedImageUrl(null);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };

  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const colorDistance = (color1, color2) => {
    return Math.sqrt(
      Math.pow(color1.r - color2.r, 2) +
      Math.pow(color1.g - color2.g, 2) +
      Math.pow(color1.b - color2.b, 2)
    );
  };

  const removeBackground = () => {
    if (!imagePreview) return;

    setIsProcessing(true);
    
    const img = new Image();
    img.onload = () => {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      canvas.width = img.width;
      canvas.height = img.height;
      
      ctx.drawImage(img, 0, 0);
      
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      const targetColor = hexToRgb(selectedColor);
      
      // Process each pixel
      for (let i = 0; i < data.length; i += 4) {
        const pixelColor = {
          r: data[i],
          g: data[i + 1],
          b: data[i + 2]
        };
        
        const distance = colorDistance(pixelColor, targetColor);
        
        // If the color is within tolerance, make it transparent
        if (distance <= tolerance) {
          data[i + 3] = 0; // Set alpha to 0 (transparent)
        }
      }
      
      ctx.putImageData(imageData, 0, 0);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setProcessedImageUrl(url);
        }
        setIsProcessing(false);
      }, 'image/png', 1);
    };
    
    img.src = imagePreview;
  };

  const downloadProcessedImage = () => {
    if (!processedImageUrl) return;
    
    const link = document.createElement('a');
    link.href = processedImageUrl;
    link.download = `background_removed_${selectedImage.name.split('.')[0]}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetTool = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setProcessedImageUrl(null);
    setTolerance(30);
    setSelectedColor('#ffffff');
    setOriginalDimensions({ width: 0, height: 0 });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const applyColorPreset = (color) => {
    setSelectedColor(color);
  };

  return (
    <>
      <SEO
        title="Background Remover - Remove Image Backgrounds Online | ToollyHub"
        description="Remove backgrounds from images easily with our online background remover tool. Support for color-based removal with adjustable tolerance."
        keywords="background remover, remove background, image background, transparent background, photo editor, background removal tool"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-6xl mx-auto">
          {/* Modern Header */}
          <div className="bg-gradient-to-r from-red-500 via-pink-500 to-purple-500 rounded-2xl shadow-xl p-8 mb-8">
            <h1 className="text-4xl font-bold text-white text-center mb-2">Background Remover</h1>
            <p className="text-red-100 text-center text-lg">Remove backgrounds from your images with precision</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Upload and Controls Section */}
            <div className="lg:col-span-1 bg-white rounded-2xl shadow-lg overflow-hidden">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 p-6">
                <h2 className="text-xl font-bold text-white">Upload & Removal Settings</h2>
                <p className="text-orange-100 mt-1">Configure background removal options</p>
              </div>
              <div className="p-6 space-y-6">
                {/* File Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Select Image</label>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  />
                </div>

                {originalDimensions.width > 0 && (
                  <>
                    {/* Original Dimensions Display */}
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Image Info</h3>
                      <p className="text-gray-600 text-sm">{originalDimensions.width} × {originalDimensions.height} pixels</p>
                      <p className="text-gray-600 text-sm">File: {selectedImage.name}</p>
                    </div>

                    {/* Color Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Background Color to Remove</label>
                      
                      {/* Color Presets */}
                      <div className="grid grid-cols-3 gap-2 mb-4">
                        {backgroundPresets.map((preset, index) => (
                          <button
                            key={index}
                            onClick={() => applyColorPreset(preset.color)}
                            className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                              selectedColor === preset.color 
                                ? 'border-red-500 ring-2 ring-red-200' 
                                : 'border-gray-200 hover:border-red-300'
                            }`}
                            style={{ backgroundColor: preset.color }}
                            title={preset.name}
                          >
                            <span className={`text-xs font-medium ${
                              preset.color === '#ffffff' || preset.color === '#00ff00' ? 'text-gray-800' : 'text-white'
                            }`}>
                              {preset.name}
                            </span>
                          </button>
                        ))}
                      </div>

                      {/* Custom Color Picker */}
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={selectedColor}
                          onChange={(e) => setSelectedColor(e.target.value)}
                          className="w-12 h-12 rounded-lg border-2 border-gray-200 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={selectedColor}
                          onChange={(e) => setSelectedColor(e.target.value)}
                          className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                          placeholder="#ffffff"
                        />
                      </div>
                    </div>

                    {/* Tolerance Setting */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Color Tolerance: {tolerance}
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={tolerance}
                        onChange={(e) => setTolerance(parseInt(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>Exact match</span>
                        <span>More flexible</span>
                      </div>
                      <p className="text-xs text-gray-600 mt-2">
                        Higher tolerance removes more similar colors
                      </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-3">
                      <button
                        onClick={removeBackground}
                        disabled={isProcessing}
                        className="w-full px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white font-semibold rounded-lg hover:from-red-600 hover:to-pink-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isProcessing ? 'Processing...' : 'Remove Background'}
                      </button>
                      
                      <button
                        onClick={resetTool}
                        className="w-full px-6 py-3 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
                      >
                        Reset Tool
                      </button>
                    </div>

                    {/* Instructions */}
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-800 mb-2">💡 Tips for Best Results</h4>
                      <ul className="text-xs text-blue-700 space-y-1">
                        <li>• Use images with solid, uniform backgrounds</li>
                        <li>• Start with low tolerance and increase if needed</li>
                        <li>• Green screens work best for removal</li>
                        <li>• High contrast between subject and background helps</li>
                      </ul>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Preview Section */}
            <div className="lg:col-span-2 space-y-6">
              {/* Original and Processed Images */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-teal-500 to-cyan-500 p-6">
                  <h2 className="text-xl font-bold text-white">Image Preview</h2>
                  <p className="text-teal-100 mt-1">Original and background-removed comparison</p>
                </div>
                <div className="p-6">
                  {imagePreview ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Original Image */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-700 mb-3">Original Image</h3>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <img
                            src={imagePreview}
                            alt="Original"
                            className="w-full h-auto max-h-64 object-contain mx-auto rounded"
                          />
                        </div>
                      </div>

                      {/* Processed Image */}
                      <div>
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="text-lg font-semibold text-gray-700">Background Removed</h3>
                          {processedImageUrl && (
                            <button
                              onClick={downloadProcessedImage}
                              className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 text-sm"
                            >
                              📥 Download PNG
                            </button>
                          )}
                        </div>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 relative">
                          {/* Transparency Checker Background */}
                          <div
                            className="absolute inset-4 rounded opacity-20"
                            style={{
                              backgroundImage: `
                                linear-gradient(45deg, #ccc 25%, transparent 25%),
                                linear-gradient(-45deg, #ccc 25%, transparent 25%),
                                linear-gradient(45deg, transparent 75%, #ccc 75%),
                                linear-gradient(-45deg, transparent 75%, #ccc 75%)
                              `,
                              backgroundSize: '20px 20px',
                              backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                            }}
                          />
                          {isProcessing ? (
                            <div className="flex items-center justify-center h-64 relative z-10">
                              <div className="text-center">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
                                <p className="text-gray-600">Removing background...</p>
                              </div>
                            </div>
                          ) : processedImageUrl ? (
                            <img
                              src={processedImageUrl}
                              alt="Background Removed"
                              className="w-full h-auto max-h-64 object-contain mx-auto rounded relative z-10"
                            />
                          ) : (
                            <div className="flex items-center justify-center h-64 text-gray-500 relative z-10">
                              <div className="text-center">
                                <svg className="mx-auto h-12 w-12 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                <p>Processed image will appear here</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        <svg className="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <p className="text-gray-500">Upload an image to start background removal</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Settings Summary */}
              {imagePreview && (
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                  <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-6">
                    <h3 className="text-xl font-bold text-white">Current Settings</h3>
                    <p className="text-indigo-100 mt-1">Background removal configuration</p>
                  </div>
                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Target Color</h4>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-8 h-8 rounded border-2 border-gray-300"
                            style={{ backgroundColor: selectedColor }}
                          ></div>
                          <span className="text-sm font-mono text-gray-600">{selectedColor}</span>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Tolerance</h4>
                        <p className="text-lg font-semibold text-gray-800">{tolerance}</p>
                        <p className="text-xs text-gray-500">
                          {tolerance < 20 ? 'Precise' : tolerance < 50 ? 'Moderate' : 'Flexible'}
                        </p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Output Format</h4>
                        <p className="text-lg font-semibold text-gray-800">PNG</p>
                        <p className="text-xs text-gray-500">Supports transparency</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Hidden Canvas for Processing */}
          <canvas ref={canvasRef} style={{ display: 'none' }} />

          {/* SEO Content */}
          <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">About Background Removal</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Remove Backgrounds Easily</h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  Our background remover tool uses advanced color-based algorithms to remove backgrounds from your images.
                  Perfect for creating transparent images, product photos, or preparing images for design projects.
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    Color-based background removal
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">•</span>
                    Adjustable tolerance settings
                  </li>
                  <li className="flex items-center">
                    <span className="text-purple-500 mr-2">•</span>
                    PNG output with transparency
                  </li>
                  <li className="flex items-center">
                    <span className="text-orange-500 mr-2">•</span>
                    Real-time preview
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Best Practices</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-red-500 mr-2">•</span>
                    <strong>Solid Backgrounds:</strong> Work best for removal
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-2">•</span>
                    <strong>High Contrast:</strong> Better separation results
                  </li>
                  <li className="flex items-center">
                    <span className="text-teal-500 mr-2">•</span>
                    <strong>Green Screen:</strong> Ideal for professional results
                  </li>
                  <li className="flex items-center">
                    <span className="text-pink-500 mr-2">•</span>
                    <strong>Good Lighting:</strong> Ensures even background color
                  </li>
                  <li className="flex items-center">
                    <span className="text-yellow-500 mr-2">•</span>
                    <strong>Start Low:</strong> Begin with low tolerance, increase as needed
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BackgroundRemover;
