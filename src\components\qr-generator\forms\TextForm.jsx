import React from 'react';
import { INPUT_STYLES, LABEL_STYLES } from '../utils/constants';

const TextForm = ({ formData, updateFormData }) => {
  return (
    <div className="space-y-2">
      <label className={LABEL_STYLES}>Text Content</label>
      <textarea
        value={formData.content}
        onChange={(e) => updateFormData('content', e.target.value)}
        className={INPUT_STYLES}
        placeholder="Enter your text here..."
        rows="4"
      />
    </div>
  );
};

export default TextForm;
